# SEVALLA ENVIRONMENT VARIABLES
# Copy these values to your Sevalla deployment dashboard

# =============================================================================
# CRITICAL VARIABLES (Required for application to start)
# =============================================================================

# Discord OAuth Configuration (Required for user authentication)
BOT_CLIENT_ID=1394521471862308884
BOT_CLIENT_SECRET=1IOBGoqWI8s4DxeX4SgF6G3dLH7vsAXD
DISCORD_CLIENT_ID=1394521471862308884
DISCORD_CLIENT_SECRET=1IOBGoqWI8s4DxeX4SgF6G3dLH7vsAXD

# =============================================================================
# OPTIONAL DISCORD BOT TOKEN (for bot functionality)
# =============================================================================
# If you want Discord bot features (slash commands, events), set this:
# DISCORD_TOKEN=MTM5NDUyMTQ3MTg2MjMwODg4NA.GWam6x.JQ8u5lCOVmRl6hnpeUkXYG4Mt-4Epy634ZKOT4
# If not set, application runs in OAuth-only mode

NODE_ENV=production
PORT=8080

# Redis Configuration (Primary Database)
REDIS_URL=redis://localhost:6379
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

USER_ENCRYPTION_KEY=5b294018a4cccfd435083d865b5199ef4d259f936d04dd8f35fd074d8ea99cbb
SESSION_ENCRYPTION_KEY=833ff390d1c4413266ee115259b642874c0d8517214cb05b45b2b87da9d48d03
CSRF_ENCRYPTION_KEY=5e9d3e69535613f9e7d797a54b5df9207a1c95d8cd05c297eee3a6cd15541ed9

# =============================================================================
# APPLICATION URLS
# =============================================================================

WEB_URL=https://discordbot-energex-jkhvk.sevalla.app
APP_URL=https://discordbot-energex-jkhvk.sevalla.app
NEXT_PUBLIC_API_ENDPOINT=https://discordbot-energex-backend-nqzv2.sevalla.app
INTERNAL_API_ENDPOINT=https://discordbot-energex-backend-nqzv2.sevalla.app

# =============================================================================
# AI CONFIGURATION
# =============================================================================

ANTHROPIC_API_KEY=************************************************************************************************************
USE_MASTRA=true
DEFAULT_AI_CHANNEL=general

# =============================================================================
# OPTIONAL SETTINGS
# =============================================================================

ENABLE_ENV_LOGIN=false
SESSION_ISOLATION_ENABLED=true
SESSION_FINGERPRINTING_ENABLED=true
AUTOMATIC_TOKEN_ROTATION=true

# =============================================================================
# INSTRUCTIONS:
# =============================================================================
# 1. Go to your Sevalla dashboard
# 2. Navigate to your backend project: discordbot-energex-backend-nqzv2
# 3. Find "Environment Variables" or "Settings" section
# 4. Add each variable above (name=value)
# 5. Save configuration
# 6. Redeploy your application
# 7. Check logs to verify successful startup
