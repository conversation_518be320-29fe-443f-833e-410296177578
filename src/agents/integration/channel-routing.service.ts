import { Injectable, Logger } from '@nestjs/common';
import { Message } from 'discord.js';
import { AgentsService } from '../agents.service';

@Injectable()
export class ChannelRoutingService {
  private readonly logger = new Logger(ChannelRoutingService.name);
  private isInitialized = false;

  // Keywords that trigger specific agents
  private readonly agentKeywords = {
    personal_growth_coach: [
      'coach', 'growth', 'develop', 'improve', 'goal', 'motivation',
      'mindset', 'habit', 'success', 'achievement', 'potential',
      'confidence', 'self-improvement', 'personal development'
    ],
    intake_specialist: [
      'intake', 'assessment', 'evaluation', 'onboard', 'start',
      'begin', 'new', 'introduction', 'first time', 'getting started',
      'questionnaire', 'survey', 'profile'
    ],
    progress_tracker: [
      'progress', 'track', 'milestone', 'achievement', 'goal',
      'status', 'update', 'report', 'check-in', 'review',
      'stats', 'analytics', 'measurement', 'performance'
    ]
  };

  // Sentiment patterns that might indicate need for specific agents
  private readonly sentimentPatterns = {
    frustrated: ['frustrated', 'stuck', 'confused', 'lost', 'overwhelmed'],
    motivated: ['excited', 'ready', 'motivated', 'determined', 'committed'],
    questioning: ['how', 'what', 'when', 'where', 'why', '?'],
  };

  constructor(private readonly agentsService: AgentsService) {}

  async initialize() {
    if (this.isInitialized) return;
    
    this.logger.log('🧭 Initializing Channel Routing Service...');
    this.isInitialized = true;
    this.logger.log('✅ Channel Routing Service initialized');
  }

  async routeMessage(message: Message): Promise<string | null> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    try {
      const content = message.content.toLowerCase();
      const userId = message.author.id;
      const guildId = message.guild?.id;

      // Check if user has an active conversation with a specific agent
      const activeAgent = await this.getActiveAgentForUser(userId);
      if (activeAgent) {
        return activeAgent;
      }

      // Analyze message for agent routing
      const routingScore = this.analyzeMessageForRouting(content);
      const bestAgent = this.getBestAgentFromScore(routingScore);

      // Check if the confidence is high enough to route
      const bestScore = routingScore[bestAgent];
      if (bestScore && bestScore >= 0.3) {
        // Store the routing decision for context
        await this.storeRoutingDecision(userId, bestAgent, bestScore);
        return bestAgent;
      }

      // If no clear agent match, check guild configuration for default agent
      if (guildId) {
        const defaultAgent = await this.getDefaultAgentForGuild(guildId);
        if (defaultAgent) {
          return defaultAgent;
        }
      }

      // No routing decision made
      return null;
    } catch (error) {
      this.logger.error('Failed to route message:', error);
      return null;
    }
  }

  private analyzeMessageForRouting(content: string): Record<string, number> {
    const scores = {
      personal_growth_coach: 0,
      intake_specialist: 0,
      progress_tracker: 0,
    };

    // Keyword matching
    for (const [agent, keywords] of Object.entries(this.agentKeywords)) {
      for (const keyword of keywords) {
        if (content.includes(keyword)) {
          (scores as any)[agent] += 0.2;
        }
      }
    }

    // Sentiment analysis (basic)
    const sentiment = this.analyzeSentiment(content);
    switch (sentiment) {
      case 'frustrated':
        scores.personal_growth_coach += 0.3;
        break;
      case 'motivated':
        scores.personal_growth_coach += 0.2;
        scores.progress_tracker += 0.1;
        break;
      case 'questioning':
        scores.intake_specialist += 0.2;
        break;
    }

    // Length-based hints
    if (content.length > 200) {
      // Longer messages might benefit from personal coaching
      scores.personal_growth_coach += 0.1;
    } else if (content.length < 50) {
      // Short messages might be status checks
      scores.progress_tracker += 0.1;
    }

    // Question detection
    if (content.includes('?')) {
      scores.intake_specialist += 0.1;
    }

    // Normalize scores
    const maxScore = Math.max(...Object.values(scores));
    if (maxScore > 0) {
      for (const agent in scores) {
        (scores as any)[agent] = (scores as any)[agent] / maxScore;
      }
    }

    return scores;
  }

  private analyzeSentiment(content: string): string | null {
    for (const [sentiment, patterns] of Object.entries(this.sentimentPatterns)) {
      for (const pattern of patterns) {
        if (content.includes(pattern)) {
          return sentiment;
        }
      }
    }
    return null;
  }

  private getBestAgentFromScore(scores: Record<string, number>): string {
    let bestAgent = 'personal_growth_coach';
    let bestScore = 0;

    for (const [agent, score] of Object.entries(scores)) {
      if (score > bestScore) {
        bestAgent = agent;
        bestScore = score;
      }
    }

    return bestAgent;
  }

  private async getActiveAgentForUser(userId: string): Promise<string | null> {
    try {
      // Check if user has recent interactions with a specific agent
      // This would typically check the database for recent conversations
      
      // For now, return null (no active agent context)
      return null;
    } catch (error) {
      this.logger.error(`Failed to get active agent for user ${userId}:`, error);
      return null;
    }
  }

  private async getDefaultAgentForGuild(guildId: string): Promise<string | null> {
    try {
      const config = await this.agentsService.getAgentConfig(guildId, 'general');
      
      if (config && config.configuration && 'defaultAgent' in config.configuration) {
        return (config.configuration as any).defaultAgent;
      }
      
      return 'personal_growth_coach'; // Default fallback
    } catch (error) {
      this.logger.error(`Failed to get default agent for guild ${guildId}:`, error);
      return null;
    }
  }

  private async storeRoutingDecision(userId: string, agent: string, confidence: number) {
    try {
      // Store the routing decision for analytics and context
      await this.agentsService.storeMemory({
        userId,
        memoryType: 'interaction',
        key: 'last_agent_route',
        value: {
          agent,
          confidence,
          timestamp: new Date(),
        },
        importance: Math.floor(confidence * 10),
      });
    } catch (error) {
      this.logger.error('Failed to store routing decision:', error);
    }
  }

  async getRoutingStats() {
    return {
      initialized: this.isInitialized,
      availableAgents: Object.keys(this.agentKeywords),
      keywordCount: Object.values(this.agentKeywords).flat().length,
      sentimentPatterns: Object.keys(this.sentimentPatterns).length,
      routingAlgorithm: 'keyword_and_sentiment_based',
    };
  }

  async analyzeMessageIntent(content: string) {
    const scores = this.analyzeMessageForRouting(content.toLowerCase());
    const sentiment = this.analyzeSentiment(content.toLowerCase());
    
    return {
      content: content.substring(0, 100) + (content.length > 100 ? '...' : ''),
      agentScores: scores,
      detectedSentiment: sentiment,
      recommendedAgent: this.getBestAgentFromScore(scores),
      confidence: Math.max(...Object.values(scores)),
      analysis: {
        wordCount: content.split(' ').length,
        hasQuestion: content.includes('?'),
        length: content.length,
      },
    };
  }
}
