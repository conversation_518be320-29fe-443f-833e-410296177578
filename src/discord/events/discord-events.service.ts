import { DatabaseService } from '@/core/database';
import { Injectable, Logger } from '@nestjs/common';
import { Client, Guild } from 'discord.js';


import { Context, ContextOf, On, Once } from 'necord';

import { guilds, users, type NewGuild, type NewUser } from '@/core/database';
import { PersonalGrowthSupportService } from '../../agents/integration/personal-growth-support.service';
import { ChannelFilterService } from '../../core/services/channel-filter.service';
// Redis operations - no need for SQL query builders

@Injectable()
export class DiscordEventsService {
  private readonly logger = new Logger(DiscordEventsService.name);

  constructor(
    private readonly databaseService: DatabaseService,
    private readonly personalGrowthSupportService: PersonalGrowthSupportService,
    private readonly channelFilterService: ChannelFilterService,
  ) {}

  @Once('ready')
  public async onReady(@Context() [client]: ContextOf<'ready'>) {
    this.logger.log(`🤖 Bot logged in as ${client.user.username}!`);
    this.logger.log(`📊 Active in ${client.guilds.cache.size} guilds`);
    
    // Sync guilds with database
    await this.syncGuildsWithDatabase(client);
  }

  @On('guildCreate')
  public async onGuildJoin(@Context() [guild]: ContextOf<'guildCreate'>) {
    this.logger.log(`📥 Bot joined guild: ${guild.name} (${guild.id})`);
    await this.addGuildToDatabase(guild);
  }

  @On('guildDelete')
  public async onGuildLeave(@Context() [guild]: ContextOf<'guildDelete'>) {
    this.logger.log(`📤 Bot left guild: ${guild.name} (${guild.id})`);
    await this.updateGuildStatus(guild.id, false);
  }

  @On('guildUpdate')
  public async onGuildUpdate(@Context() [oldGuild, newGuild]: ContextOf<'guildUpdate'>) {
    this.logger.log(`🔄 Guild updated: ${newGuild.name} (${newGuild.id})`);
    await this.updateGuildInfo(newGuild);
  }

  @On('guildMemberAdd')
  public async onMemberJoin(@Context() [member]: ContextOf<'guildMemberAdd'>) {
    this.logger.log(`👋 Member joined ${member.guild.name}: ${member.user.username}`);
    await this.addUserToDatabase(member.user, member.guild.id);
    
    // Send personalized welcome message with personal growth support
    await this.personalGrowthSupportService.welcomeNewMember(member);
    
    this.logger.log(`Welcome system processed for ${member.user.username}`);
  }

  @On('messageCreate')
  public async onMessage(@Context() [message]: ContextOf<'messageCreate'>) {
    // Use centralized channel filtering service
    if (!this.channelFilterService.shouldProcessMessage(message)) {
      return;
    }
    
    // Only process messages in guilds (not DMs)
    if (!message.guild) return;

    try {
      // Update user activity and handle personal growth support
      await this.personalGrowthSupportService.updateUserActivity(message.author.id);
      await this.personalGrowthSupportService.analyzeAndSupport(message);
      
    } catch (error) {
      this.logger.error('Error processing message:', error);
    }
  }

  @On('error')
  public onError(@Context() [error]: ContextOf<'error'>) {
    this.logger.error('Discord client error:', error);
  }

  @On('warn')
  public onWarn(@Context() [warning]: ContextOf<'warn'>) {
    this.logger.warn('Discord client warning:', warning);
  }

  @On('shardError')
  public onShardError(@Context() [error, shardId]: ContextOf<'shardError'>) {
    this.logger.error(`Shard ${shardId} error:`, error);
  }

  /**
   * Sync all guilds with database on startup
   */
  private async syncGuildsWithDatabase(client: Client) {
    try {
      const guilds = Array.from(client.guilds.cache.values());
      
      for (const guild of guilds) {
        await this.addGuildToDatabase(guild);
      }
      
      this.logger.log(`✅ Synced ${guilds.length} guilds with database`);
    } catch (error) {
      this.logger.error('Failed to sync guilds with database:', error);
    }
  }

  /**
   * Add or update guild in database
   */
  private async addGuildToDatabase(guild: Guild) {
    try {
      const guildData = {
        discordId: guild.id,
        name: guild.name,
        icon: guild.iconURL() || undefined,
        ownerDiscordId: guild.ownerId,
        isActive: true,
        lastActivityAt: new Date(),
      };

      await this.db.insert(guilds).values(guildData as NewGuild)
        .onConflictDoUpdate({
          target: guilds.discordId,
          set: {
            name: guild.name,
            icon: guild.iconURL() || undefined,
            ownerDiscordId: guild.ownerId,
            isActive: true,
            lastActivityAt: new Date(),
          } as Partial<NewGuild>
        });
    } catch (error) {
      this.logger.error(`Failed to add guild ${guild.id} to database:`, error);
    }
  }

  /**
   * Update guild status in database
   */
  private async updateGuildStatus(guildId: string, isActive: boolean) {
    try {
      await this.db
        .update(guilds)
        .set({ isActive, lastActivityAt: new Date() } as Partial<NewGuild>)
        .where(eq(guilds.discordId, guildId));
    } catch (error) {
      this.logger.error(`Failed to update guild ${guildId} status:`, error);
    }
  }

  /**
   * Update guild information in database
   */
  private async updateGuildInfo(guild: Guild) {
    try {
      await this.db
        .update(guilds)
        .set({
          name: guild.name,
          icon: guild.iconURL() || undefined,
          ownerDiscordId: guild.ownerId,
          lastActivityAt: new Date(),
        } as Partial<NewGuild>)
        .where(eq(guilds.discordId, guild.id));
    } catch (error) {
      this.logger.error(`Failed to update guild ${guild.id} info:`, error);
    }
  }

  /**
   * Add user to database
   */
  private async addUserToDatabase(user: any, guildId: string) {
    try {
      const userData = {
        discordId: user.id,
        username: user.username,
        isActive: true,
        lastActivityAt: new Date(),
      };

      await this.db.insert(users).values(userData as NewUser)
        .onConflictDoUpdate({
          target: users.discordId,
          set: {
            username: userData.username,
            isActive: true,
            lastActivityAt: new Date(),
          } as Partial<NewUser>
        });
    } catch (error) {
      this.logger.error(`Failed to add user ${user.id} to database:`, error);
    }
  }

  /**
   * Update user activity in database
   */
  private async updateUserActivity(userId: string) {
    try {
      await this.db
        .update(users)
        .set({ lastActivityAt: new Date() } as Partial<NewUser>)
        .where(eq(users.discordId, userId));
    } catch (error) {
      this.logger.error(`Failed to update user activity for ${userId}:`, error);
    }
  }
}