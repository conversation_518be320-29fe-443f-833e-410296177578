import { Injectable, Logger } from '@nestjs/common';
import {
    Context,
    Options,
    SlashCommand,
    SlashCommandContext,
    StringOption
} from 'necord';
import { DiscordService } from '../discord.service';

class PingDto {
  @StringOption({
    name: 'message',
    description: 'Optional message to include with ping',
    required: false,
  })
  message?: string;
}

class RefreshDto {
  @StringOption({
    name: 'component',
    description: 'Specific component to refresh (commands, agents, config, all)',
    required: false,
    choices: [
      { name: 'Commands', value: 'commands' },
      { name: 'AI Agents', value: 'agents' },
      { name: 'Configuration', value: 'config' },
      { name: 'All Components', value: 'all' },
    ],
  })
  component?: 'commands' | 'agents' | 'config' | 'all';
}

@Injectable()
export class DiscordCommandsService {
  private readonly logger = new Logger(DiscordCommandsService.name);

  constructor(
    private readonly discordService: DiscordService,
  ) {}

  @SlashCommand({
    name: 'ping',
    description: 'Check if the bot is responsive',
  })
  public async onPing(
    @Context() [interaction]: SlashCommandContext,
    @Options() options: PingDto,
  ) {
    try {
      const ping = this.discordService.getClient().ws.ping;
      const message = options.message ? ` | ${options.message}` : '';
      
      await interaction.reply({
        content: `🏓 Pong! Bot latency: ${ping}ms${message}`,
        ephemeral: true,
      });
      
      this.logger.log(`Ping command executed by ${interaction.user.username}`);
    } catch (error) {
      this.logger.error('Ping command failed:', error);
      await interaction.reply({
        content: '❌ Failed to execute ping command',
        ephemeral: true,
      });
    }
  }

  @SlashCommand({
    name: 'status',
    description: 'Get bot status information',
  })
  public async onStatus(
    @Context() [interaction]: SlashCommandContext,
  ) {
    try {
      const health = await this.discordService.healthCheck();
      const uptime = process.uptime();
      const uptimeHours = Math.floor(uptime / 3600);
      const uptimeMinutes = Math.floor((uptime % 3600) / 60);
      
      const embed = {
        title: '🤖 Bot Status',
        color: health.status === 'healthy' ? 0x00ff00 : 0xff0000,
        fields: [
          {
            name: '🔗 Connection',
            value: health.connected ? 'Connected' : 'Disconnected',
            inline: true,
          },
          {
            name: '📊 Guilds',
            value: health.guilds.toString(),
            inline: true,
          },
          {
            name: '👥 Users',
            value: health.users.toString(),
            inline: true,
          },
          {
            name: '📡 Ping',
            value: `${health.ping}ms`,
            inline: true,
          },
          {
            name: '⏱️ Uptime',
            value: `${uptimeHours}h ${uptimeMinutes}m`,
            inline: true,
          },
          {
            name: '💾 Memory',
            value: `${Math.round(process.memoryUsage().heapUsed / 1024 / 1024)}MB`,
            inline: true,
          },
        ],
        timestamp: new Date(),
      };
      
      await interaction.reply({
        embeds: [embed],
        ephemeral: true,
      });
      
      this.logger.log(`Status command executed by ${interaction.user.username}`);
    } catch (error) {
      this.logger.error('Status command failed:', error);
      await interaction.reply({
        content: '❌ Failed to retrieve bot status',
        ephemeral: true,
      });
    }
  }

  @SlashCommand({
    name: 'help',
    description: 'Get help information about bot commands',
  })
  public async onHelp(
    @Context() [interaction]: SlashCommandContext,
  ) {
    try {
      const embed = {
        title: '📚 Bot Help',
        description: 'Available commands and features:',
        color: 0x0099ff,
        fields: [
          {
            name: '🏓 /ping',
            value: 'Check if the bot is responsive',
            inline: false,
          },
          {
            name: '📊 /status',
            value: 'Get detailed bot status information',
            inline: false,
          },
          {
            name: '🔄 /refresh',
            value: 'Refresh bot settings and commands (Admin only)',
            inline: false,
          },
          {
            name: '🤖 AI Agent Commands',
            value: '`/coach` - Personal Growth Coach\n`/intake` - Intake Specialist\n`/progress` - Progress Tracker',
            inline: false,
          },
          {
            name: '⚙️ Features',
            value: 'Music, Gaming, Leveling, Economy, Moderation, and more',
            inline: false,
          },
        ],
        footer: {
          text: 'Use the dashboard for advanced configuration',
        },
        timestamp: new Date(),
      };
      
      await interaction.reply({
        embeds: [embed],
        ephemeral: true,
      });
      
      this.logger.log(`Help command executed by ${interaction.user.username}`);
    } catch (error) {
      this.logger.error('Help command failed:', error);
      await interaction.reply({
        content: '❌ Failed to display help information',
        ephemeral: true,
      });
    }
  }

  @SlashCommand({
    name: 'refresh',
    description: 'Refresh bot settings and commands (Admin only)',
  })
  public async onRefresh(
    @Context() [interaction]: SlashCommandContext,
    @Options() options: RefreshDto,
  ) {
    try {
      // Check if user has admin permissions
      const member = interaction.guild?.members.cache.get(interaction.user.id);
      const hasAdminPermission = member?.permissions.has('Administrator') ||
                                member?.permissions.has('ManageGuild');

      if (!hasAdminPermission) {
        await interaction.reply({
          content: '❌ You need Administrator or Manage Server permissions to use this command.',
          ephemeral: true,
        });
        return;
      }

      await interaction.deferReply({ ephemeral: true });

      const component = options.component || 'all';
      const refreshResults: string[] = [];
      let hasErrors = false;

      // Refresh Commands
      if (component === 'commands' || component === 'all') {
        try {
          await this.refreshCommands(interaction.guild?.id);
          refreshResults.push('✅ Commands refreshed successfully');
        } catch (error) {
          this.logger.error('Failed to refresh commands:', error);
          refreshResults.push('❌ Failed to refresh commands');
          hasErrors = true;
        }
      }

      // Refresh AI Agents
      if (component === 'agents' || component === 'all') {
        try {
          await this.refreshAgents(interaction.guild?.id);
          refreshResults.push('✅ AI Agents refreshed successfully');
        } catch (error) {
          this.logger.error('Failed to refresh AI agents:', error);
          refreshResults.push('❌ Failed to refresh AI agents');
          hasErrors = true;
        }
      }

      // Refresh Configuration
      if (component === 'config' || component === 'all') {
        try {
          await this.refreshConfiguration();
          refreshResults.push('✅ Configuration refreshed successfully');
        } catch (error) {
          this.logger.error('Failed to refresh configuration:', error);
          refreshResults.push('❌ Failed to refresh configuration');
          hasErrors = true;
        }
      }

      const embed = {
        title: '🔄 Bot Refresh Results',
        description: refreshResults.join('\n'),
        color: hasErrors ? 0xff9900 : 0x00ff00,
        fields: [
          {
            name: '📊 Component',
            value: component === 'all' ? 'All Components' : component.charAt(0).toUpperCase() + component.slice(1),
            inline: true,
          },
          {
            name: '⏱️ Timestamp',
            value: new Date().toLocaleString(),
            inline: true,
          },
          {
            name: '👤 Executed By',
            value: interaction.user.username,
            inline: true,
          },
        ],
        footer: {
          text: hasErrors ? 'Some components failed to refresh - check logs for details' : 'All components refreshed successfully',
        },
      };

      await interaction.editReply({ embeds: [embed] });

      this.logger.log(`Refresh command executed by ${interaction.user.username} for component: ${component}`);
    } catch (error) {
      this.logger.error('Refresh command failed:', error);

      const errorMessage = interaction.deferred
        ? { content: '❌ Failed to refresh bot components. Please check logs for details.' }
        : { content: '❌ Failed to refresh bot components. Please check logs for details.', ephemeral: true };

      if (interaction.deferred) {
        await interaction.editReply(errorMessage);
      } else {
        await interaction.reply(errorMessage);
      }
    }
  }

  private async refreshCommands(guildId?: string): Promise<void> {
    try {
      // Get the Discord client
      const client = this.discordService.getClient();

      if (!client.isReady()) {
        throw new Error('Discord client is not ready');
      }

      // Clear command cache if it exists
      if (client.application?.commands) {
        await client.application.commands.fetch();
        this.logger.log('✅ Command cache refreshed');
      }

      // If guild-specific, refresh guild commands
      if (guildId) {
        const guild = client.guilds.cache.get(guildId);
        if (guild) {
          await guild.commands.fetch();
          this.logger.log(`✅ Guild commands refreshed for ${guild.name}`);
        }
      }

      this.logger.log('Commands refresh completed successfully');
    } catch (error) {
      this.logger.error('Failed to refresh commands:', error);
      throw error;
    }
  }

  private async refreshAgents(guildId?: string): Promise<void> {
    try {
      // Clear any cached agent configurations
      // This would typically involve clearing caches and reloading agent configs

      this.logger.log('AI Agents refresh completed successfully');
    } catch (error) {
      this.logger.error('Failed to refresh AI agents:', error);
      throw error;
    }
  }

  private async refreshConfiguration(): Promise<void> {
    try {
      // Refresh environment configuration
      // This could involve reloading config files or clearing config caches

      this.logger.log('Configuration refresh completed successfully');
    } catch (error) {
      this.logger.error('Failed to refresh configuration:', error);
      throw error;
    }
  }
}
