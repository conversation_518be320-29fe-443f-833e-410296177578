
import { BaseEntity } from '../types/base.interface';

// Create and Update entity types
export type CreateEntity<T> = Omit<T, 'id' | 'createdAt' | 'updatedAt'>;
export type UpdateEntity<T> = Partial<Omit<T, 'id' | 'createdAt'>>;

// Market types
export type MarketType = 'stocks' | 'crypto' | 'forex' | 'commodities';
export type RiskLevel = 'low' | 'medium' | 'high' | 'aggressive';

/**
 * Market Data entity interface - Redis compatible
 */
export interface MarketData extends BaseEntity {
  symbol: string;
  name: string;
  market: MarketType;
  currentPrice: number;
  previousClose?: number;
  change?: number;
  changePercent?: number;
  volume?: number;
  marketCap?: number;
  high24h?: number;
  low24h?: number;
  lastUpdated: Date;
  isActive: boolean;
  metadata?: Record<string, any>;
}

/**
 * Trading Portfolio entity interface - Redis compatible
 */
export interface TradingPortfolio extends BaseEntity {
  userId: string;
  name: string;
  description?: string;
  totalValue: number;
  totalCost: number;
  totalGainLoss: number;
  totalGainLossPercent: number;
  isDefault: boolean;
  isPublic: boolean;
  riskLevel: RiskLevel;
  strategy?: string;
  guildId: string;
  lastUpdated: Date;
}

/**
 * Portfolio Holdings entity interface - Redis compatible
 */
export interface PortfolioHolding extends BaseEntity {
  portfolioId: string;
  symbol: string;
  quantity: number;
  averageCost: number;
  currentPrice: number;
  totalCost: number;
  currentValue: number;
  gainLoss: number;
  gainLossPercent: number;
  allocation: number; // percentage of portfolio
  firstPurchaseDate: Date;
  lastUpdated: Date;
}

// Transaction types
export type TransactionType = 'buy' | 'sell' | 'dividend' | 'split' | 'transfer';
export type AlertType = 'price_above' | 'price_below' | 'volume_spike' | 'rsi_overbought' | 'rsi_oversold' | 'moving_average_cross';
export type StrategyType = 'swing' | 'day' | 'scalping' | 'position' | 'arbitrage';

/**
 * Trading Transaction entity interface - Redis compatible
 */
export interface TradingTransaction extends BaseEntity {
  portfolioId: string;
  symbol: string;
  type: TransactionType;
  quantity: number;
  price: number;
  totalAmount: number;
  fees: number;
  transactionDate: Date;
  platform?: string;
  notes?: string;
  isExecuted: boolean;
}

/**
 * Trading Alert entity interface - Redis compatible
 */
export interface TradingAlert extends BaseEntity {
  userId: string;
  symbol: string;
  alertType: AlertType;
  condition: string;
  targetValue: number;
  currentValue?: number;
  isActive: boolean;
  triggeredAt?: Date;
  notificationSent: boolean;
  guildId: string;
  metadata?: Record<string, any>;
}

/**
 * Trading Strategy entity interface - Redis compatible
 */
export interface TradingStrategy extends BaseEntity {
  userId: string;
  name: string;
  description: string;
  type: StrategyType;
  rules?: Array<{
    condition: string;
    action: string;
    parameters: Record<string, any>;
  }>;
  backtestResults?: {
    totalReturn: number;
    sharpeRatio: number;
    maxDrawdown: number;
    winRate: number;
    avgWin: number;
    avgLoss: number;
  };
  isActive: boolean;
  isPublic: boolean;
  performanceMetrics?: Record<string, any>;
  guildId: string;
}

// Analysis types
export type AnalysisType = 'technical' | 'fundamental' | 'sentiment';
export type Timeframe = '1m' | '5m' | '1h' | '1d' | '1w' | '1M';
export type TradeType = 'long' | 'short';
export type SignalType = 'buy' | 'sell' | 'hold';

/**
 * Market Analysis entity interface - Redis compatible
 */
export interface MarketAnalysis extends BaseEntity {
  symbol: string;
  analysisType: AnalysisType;
  timeframe: Timeframe;
  indicators?: Record<string, any>;
  signals?: Array<{
    type: SignalType;
    strength: number; // 1-10
    reason: string;
  }>;
  priceTargets?: {
    support: number[];
    resistance: number[];
    target: number;
    stopLoss: number;
  };
  confidence: number; // 1-100
  createdBy: string;
  guildId: string;
}

/**
 * Trading Journal entity interface - Redis compatible
 */
export interface TradingJournal extends BaseEntity {
  userId: string;
  transactionId?: string;
  symbol: string;
  entryDate: Date;
  exitDate?: Date;
  entryPrice: number;
  exitPrice?: number;
  quantity: number;
  tradeType: TradeType;
  strategy?: string;
  setup?: string;
  reasoning?: string;
  outcome?: string;
  lessons?: string;
  pnl?: number;
  pnlPercent?: number;
  emotions?: string;
  tags?: string[];
  screenshots?: string[];
  guildId: string;
}

/**
 * Watchlist entity interface - Redis compatible
 */
export interface Watchlist extends BaseEntity {
  userId: string;
  name: string;
  description?: string;
  symbols?: string[];
  isPublic: boolean;
  guildId: string;
}

// Create and Update types
export type CreateMarketData = CreateEntity<MarketData>;
export type UpdateMarketData = UpdateEntity<MarketData>;
export type CreateTradingPortfolio = CreateEntity<TradingPortfolio>;
export type UpdateTradingPortfolio = UpdateEntity<TradingPortfolio>;
export type CreatePortfolioHolding = CreateEntity<PortfolioHolding>;
export type UpdatePortfolioHolding = UpdateEntity<PortfolioHolding>;
export type CreateTradingTransaction = CreateEntity<TradingTransaction>;
export type UpdateTradingTransaction = UpdateEntity<TradingTransaction>;
export type CreateTradingAlert = CreateEntity<TradingAlert>;
export type UpdateTradingAlert = UpdateEntity<TradingAlert>;
export type CreateTradingStrategy = CreateEntity<TradingStrategy>;
export type UpdateTradingStrategy = UpdateEntity<TradingStrategy>;
export type CreateMarketAnalysis = CreateEntity<MarketAnalysis>;
export type UpdateMarketAnalysis = UpdateEntity<MarketAnalysis>;
export type CreateTradingJournal = CreateEntity<TradingJournal>;
export type UpdateTradingJournal = UpdateEntity<TradingJournal>;
export type CreateWatchlist = CreateEntity<Watchlist>;
export type UpdateWatchlist = UpdateEntity<Watchlist>;

// Legacy compatibility exports
export const marketData = {
  $inferSelect: {} as MarketData,
  $inferInsert: {} as CreateMarketData
} as const;

export const tradingPortfolios = {
  $inferSelect: {} as TradingPortfolio,
  $inferInsert: {} as CreateTradingPortfolio
} as const;

export const portfolioHoldings = {
  $inferSelect: {} as PortfolioHolding,
  $inferInsert: {} as CreatePortfolioHolding
} as const;

export const tradingTransactions = {
  $inferSelect: {} as TradingTransaction,
  $inferInsert: {} as CreateTradingTransaction
} as const;

export const tradingAlerts = {
  $inferSelect: {} as TradingAlert,
  $inferInsert: {} as CreateTradingAlert
} as const;

export const tradingStrategies = {
  $inferSelect: {} as TradingStrategy,
  $inferInsert: {} as CreateTradingStrategy
} as const;

export const marketAnalysis = {
  $inferSelect: {} as MarketAnalysis,
  $inferInsert: {} as CreateMarketAnalysis
} as const;

export const tradingJournal = {
  $inferSelect: {} as TradingJournal,
  $inferInsert: {} as CreateTradingJournal
} as const;

export const watchlists = {
  $inferSelect: {} as Watchlist,
  $inferInsert: {} as CreateWatchlist
} as const;
