import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { RedisDatabaseService } from './redis-database.service';

export interface EntityMigration {
  version: string;
  entityType: string;
  up: (redisDb: RedisDatabaseService) => Promise<void>;
  down: (redisDb: RedisDatabaseService) => Promise<void>;
}

export interface EntitySchema {
  name: string;
  indexes: string[];
  relationships?: Record<string, string>;
  ttl?: number;
  version: string;
}

@Injectable()
export class RedisEntityManagerService implements OnModuleInit {
  private readonly logger = new Logger(RedisEntityManagerService.name);
  
  // Entity schemas definition
  private readonly entitySchemas: EntitySchema[] = [
    // User entities
    {
      name: 'user',
      indexes: ['discordId', 'guildId', 'isActive', 'username'],
      version: '1.0.0'
    },

    // Community entities
    {
      name: 'community_event',
      indexes: ['guildId', 'organizerId', 'type', 'isActive', 'startDate'],
      relationships: {
        participants: 'event_participant',
        feedback: 'community_feedback'
      },
      version: '1.0.0'
    },
    {
      name: 'event_participant',
      indexes: ['eventId', 'userId', 'status', 'joinedAt'],
      version: '1.0.0'
    },
    {
      name: 'community_feedback',
      indexes: ['guildId', 'userId', 'category', 'status', 'votes'],
      version: '1.0.0'
    },
    {
      name: 'leaderboard_entry',
      indexes: ['guildId', 'userId', 'level', 'points'],
      version: '1.0.0'
    },

    // Support entities
    {
      name: 'support_ticket',
      indexes: ['userId', 'guildId', 'status', 'priority', 'category', 'assignedTo'],
      relationships: {
        responses: 'ticket_response'
      },
      version: '1.0.0'
    },
    {
      name: 'ticket_response',
      indexes: ['ticketId', 'userId', 'isStaff', 'createdAt'],
      version: '1.0.0'
    },
    {
      name: 'knowledge_base_article',
      indexes: ['category', 'isPublished', 'authorId', 'views', 'tags'],
      version: '1.0.0'
    },
    {
      name: 'troubleshooting_guide',
      indexes: ['category', 'difficulty', 'isActive', 'successRate'],
      version: '1.0.0'
    },
    {
      name: 'system_status',
      indexes: ['service', 'status', 'lastChecked'],
      version: '1.0.0'
    },

    // AI Mastery entities
    {
      name: 'ai_tool',
      indexes: ['category', 'isActive', 'addedBy', 'rating', 'votes'],
      relationships: {
        bookmarks: 'ai_tool_bookmark'
      },
      version: '1.0.0'
    },
    {
      name: 'ai_tool_bookmark',
      indexes: ['userId', 'toolId', 'rating'],
      version: '1.0.0'
    },
    {
      name: 'ai_tutorial',
      indexes: ['category', 'difficulty', 'isPublished', 'authorId', 'rating'],
      relationships: {
        progress: 'tutorial_progress'
      },
      version: '1.0.0'
    },
    {
      name: 'tutorial_progress',
      indexes: ['userId', 'tutorialId', 'isCompleted', 'progress'],
      version: '1.0.0'
    },
    {
      name: 'ai_news',
      indexes: ['category', 'importance', 'isBreaking', 'authorId', 'publishedAt'],
      version: '1.0.0'
    },
    {
      name: 'ai_user_preferences',
      indexes: ['userId', 'preferredCategories', 'difficultyLevel'],
      version: '1.0.0'
    },

    // Announcement entities
    {
      name: 'announcement',
      indexes: ['guildId', 'authorId', 'type', 'priority', 'status', 'isPinned'],
      relationships: {
        deliveries: 'announcement_delivery',
        reactions: 'announcement_reaction',
        comments: 'announcement_comment'
      },
      version: '1.0.0'
    },
    {
      name: 'announcement_delivery',
      indexes: ['announcementId', 'channelId', 'isSuccessful'],
      version: '1.0.0'
    },
    {
      name: 'announcement_subscription',
      indexes: ['userId', 'guildId', 'isEnabled'],
      version: '1.0.0'
    },
    {
      name: 'announcement_reaction',
      indexes: ['announcementId', 'userId', 'emoji'],
      version: '1.0.0'
    },
    {
      name: 'announcement_comment',
      indexes: ['announcementId', 'userId', 'parentId', 'isDeleted'],
      version: '1.0.0'
    },
    {
      name: 'announcement_acknowledgment',
      indexes: ['announcementId', 'userId', 'acknowledgedAt'],
      version: '1.0.0'
    },
    {
      name: 'announcement_template',
      indexes: ['guildId', 'creatorId', 'isPublic', 'isActive', 'category'],
      version: '1.0.0'
    },

    // Trading entities
    {
      name: 'trading_portfolio',
      indexes: ['userId', 'guildId', 'isPublic', 'totalValue'],
      relationships: {
        holdings: 'portfolio_holding',
        transactions: 'trading_transaction'
      },
      version: '1.0.0'
    },
    {
      name: 'portfolio_holding',
      indexes: ['portfolioId', 'symbol', 'marketValue'],
      version: '1.0.0'
    },
    {
      name: 'trading_alert',
      indexes: ['userId', 'guildId', 'symbol', 'isActive', 'isTriggered'],
      version: '1.0.0'
    },
    {
      name: 'trading_strategy',
      indexes: ['userId', 'guildId', 'isPublic', 'performance'],
      version: '1.0.0'
    },
    {
      name: 'trading_transaction',
      indexes: ['portfolioId', 'symbol', 'type', 'executedAt'],
      version: '1.0.0'
    },
    {
      name: 'watchlist',
      indexes: ['userId', 'guildId', 'isDefault', 'isPublic'],
      version: '1.0.0'
    },
    {
      name: 'market_data',
      indexes: ['symbol', 'lastUpdated', 'changePercent'],
      ttl: 300, // 5 minutes TTL for market data
      version: '1.0.0'
    },

    // Channel panel entities
    {
      name: 'panel_deployment',
      indexes: ['guildId', 'channelId', 'type', 'isActive'],
      version: '1.0.0'
    },
    {
      name: 'panel_analytics',
      indexes: ['panelId', 'interactionType', 'timestamp'],
      ttl: 2592000, // 30 days TTL for analytics
      version: '1.0.0'
    },
    {
      name: 'user_panel_state',
      indexes: ['userId', 'panelId', 'guildId'],
      version: '1.0.0'
    },

    // Session and security entities
    {
      name: 'session',
      indexes: ['userId', 'sessionId', 'isActive'],
      ttl: 86400, // 24 hours TTL for sessions
      version: '1.0.0'
    },
    {
      name: 'user_api_keys',
      indexes: ['userId', 'keyId', 'isActive'],
      version: '1.0.0'
    },

    // Agent and AI entities
    {
      name: 'agent_interaction',
      indexes: ['agentId', 'userId', 'interactionType', 'timestamp'],
      version: '1.0.0'
    },
    {
      name: 'agent_memory',
      indexes: ['agentId', 'userId', 'memoryType', 'importance'],
      version: '1.0.0'
    },
    {
      name: 'ai_agent_config',
      indexes: ['guildId', 'agentType', 'isActive'],
      version: '1.0.0'
    },
    {
      name: 'ai_channel_config',
      indexes: ['guildId', 'channelId', 'isActive'],
      version: '1.0.0'
    },
    {
      name: 'ai_chat_session',
      indexes: ['userId', 'channelId', 'agentId', 'isActive'],
      ttl: 3600, // 1 hour TTL for chat sessions
      version: '1.0.0'
    }
  ];

  private readonly migrations: EntityMigration[] = [
    {
      version: '1.0.0',
      entityType: 'initial_setup',
      up: async (redisDb: RedisDatabaseService) => {
        // Initial migration - create basic counters and indexes
        this.logger.log('Running initial setup migration...');
        
        // Initialize entity counters
        for (const schema of this.entitySchemas) {
          const counterKey = `seq:${schema.name}`;
          const exists = await redisDb['redis'].exists(counterKey);
          if (!exists) {
            await redisDb['redis'].set(counterKey, '0');
          }
        }
        
        this.logger.log('Initial setup migration completed');
      },
      down: async (redisDb: RedisDatabaseService) => {
        // Rollback initial setup
        this.logger.log('Rolling back initial setup...');
        
        for (const schema of this.entitySchemas) {
          const counterKey = `seq:${schema.name}`;
          await redisDb['redis'].del(counterKey);
        }
        
        this.logger.log('Initial setup rollback completed');
      }
    }
  ];

  constructor(private readonly redisDb: RedisDatabaseService) {}

  async onModuleInit() {
    await this.initializeSchemas();
    await this.runMigrations();
  }

  private async initializeSchemas(): Promise<void> {
    try {
      this.logger.log('Initializing Redis entity schemas...');
      
      // Store schema information in Redis
      for (const schema of this.entitySchemas) {
        const schemaKey = `schema:${schema.name}`;
        await this.redisDb['redis'].hset(schemaKey, {
          name: schema.name,
          indexes: JSON.stringify(schema.indexes),
          relationships: JSON.stringify(schema.relationships || {}),
          ttl: schema.ttl?.toString() || '',
          version: schema.version,
          createdAt: new Date().toISOString()
        });
      }
      
      this.logger.log(`Initialized ${this.entitySchemas.length} entity schemas`);
    } catch (error) {
      this.logger.error('Failed to initialize schemas:', error);
      throw error;
    }
  }

  private async runMigrations(): Promise<void> {
    try {
      this.logger.log('Checking for pending migrations...');
      
      const migrationKey = 'migrations:executed';
      const executedMigrations = await this.redisDb['redis'].smembers(migrationKey);
      
      for (const migration of this.migrations) {
        const migrationId = `${migration.version}:${migration.entityType}`;
        
        if (!executedMigrations.includes(migrationId)) {
          this.logger.log(`Running migration: ${migrationId}`);
          
          try {
            await migration.up(this.redisDb);
            await this.redisDb['redis'].sadd(migrationKey, migrationId);
            
            // Record migration execution
            const migrationRecordKey = `migration:${migrationId}`;
            await this.redisDb['redis'].hset(migrationRecordKey, {
              version: migration.version,
              entityType: migration.entityType,
              executedAt: new Date().toISOString(),
              status: 'completed'
            });
            
            this.logger.log(`Migration completed: ${migrationId}`);
          } catch (error) {
            this.logger.error(`Migration failed: ${migrationId}`, error);
            
            // Record failed migration
            const migrationRecordKey = `migration:${migrationId}`;
            await this.redisDb['redis'].hset(migrationRecordKey, {
              version: migration.version,
              entityType: migration.entityType,
              executedAt: new Date().toISOString(),
              status: 'failed',
              error: (error as Error).message
            });
            
            throw error;
          }
        }
      }
      
      this.logger.log('All migrations completed successfully');
    } catch (error) {
      this.logger.error('Migration process failed:', error);
      throw error;
    }
  }

  // Schema management methods
  async getEntitySchema(entityType: string): Promise<EntitySchema | null> {
    try {
      const schemaKey = `schema:${entityType}`;
      const schemaData = await this.redisDb['redis'].hgetall(schemaKey);
      
      if (Object.keys(schemaData).length === 0) {
        return null;
      }
      
      const schema: EntitySchema = {
        name: schemaData.name || entityType,
        indexes: JSON.parse(schemaData.indexes || '[]'),
        relationships: JSON.parse(schemaData.relationships || '{}'),
        version: schemaData.version || '1.0.0'
      };

      if (schemaData.ttl) {
        schema.ttl = parseInt(schemaData.ttl);
      }

      return schema;
    } catch (error) {
      this.logger.error(`Failed to get schema for ${entityType}:`, error);
      return null;
    }
  }

  async getAllSchemas(): Promise<EntitySchema[]> {
    try {
      const schemaKeys = await this.redisDb['redis'].keys('schema:*');
      const schemas: EntitySchema[] = [];
      
      for (const key of schemaKeys) {
        const entityType = key.replace('schema:', '');
        const schema = await this.getEntitySchema(entityType);
        if (schema) {
          schemas.push(schema);
        }
      }
      
      return schemas;
    } catch (error) {
      this.logger.error('Failed to get all schemas:', error);
      return [];
    }
  }

  // Migration management methods
  async rollbackMigration(version: string, entityType: string): Promise<void> {
    try {
      const migrationId = `${version}:${entityType}`;
      const migration = this.migrations.find(m => 
        m.version === version && m.entityType === entityType
      );
      
      if (!migration) {
        throw new Error(`Migration not found: ${migrationId}`);
      }
      
      this.logger.log(`Rolling back migration: ${migrationId}`);
      
      await migration.down(this.redisDb);
      
      // Remove from executed migrations
      const migrationKey = 'migrations:executed';
      await this.redisDb['redis'].srem(migrationKey, migrationId);
      
      // Update migration record
      const migrationRecordKey = `migration:${migrationId}`;
      await this.redisDb['redis'].hset(migrationRecordKey, {
        rolledBackAt: new Date().toISOString(),
        status: 'rolled_back'
      });
      
      this.logger.log(`Migration rolled back: ${migrationId}`);
    } catch (error) {
      this.logger.error('Failed to rollback migration:', error);
      throw error;
    }
  }

  // Entity validation methods
  async validateEntity(entityType: string, data: Record<string, any>): Promise<{
    isValid: boolean;
    errors: string[];
  }> {
    const errors: string[] = [];
    
    try {
      const schema = await this.getEntitySchema(entityType);
      if (!schema) {
        errors.push(`Schema not found for entity type: ${entityType}`);
        return { isValid: false, errors };
      }
      
      // Basic validation
      if (!data.id) {
        errors.push('Entity must have an id');
      }
      
      if (!data.createdAt) {
        errors.push('Entity must have createdAt timestamp');
      }
      
      if (!data.updatedAt) {
        errors.push('Entity must have updatedAt timestamp');
      }
      
      // Validate indexed fields exist (for searchability)
      for (const indexField of schema.indexes) {
        if (data[indexField] === undefined || data[indexField] === null) {
          // Only require non-optional indexed fields
          if (!this.isOptionalField(entityType, indexField)) {
            errors.push(`Indexed field '${indexField}' is required for entity type '${entityType}'`);
          }
        }
      }
      
      return {
        isValid: errors.length === 0,
        errors
      };
    } catch (error) {
      errors.push(`Validation error: ${(error as Error).message}`);
      return { isValid: false, errors };
    }
  }

  private isOptionalField(entityType: string, fieldName: string): boolean {
    // Define optional indexed fields per entity type
    const optionalFields: Record<string, string[]> = {
      user: ['guildId'],
      announcement: ['channelId'],
      trading_portfolio: ['strategy'],
      // Add more as needed
    };
    
    return optionalFields[entityType]?.includes(fieldName) || false;
  }

  // Health check and diagnostics
  async getDatabaseHealth(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy';
    details: {
      totalEntities: number;
      totalSchemas: number;
      executedMigrations: number;
      indexHealth: Record<string, number>;
    };
  }> {
    try {
      const schemas = await this.getAllSchemas();
      const executedMigrations = await this.redisDb['redis'].smembers('migrations:executed');
      
      // Count entities by type
      const entityCounts: Record<string, number> = {};
      let totalEntities = 0;
      
      for (const schema of schemas) {
        const listKey = `list:${schema.name}:all`;
        const count = await this.redisDb['redis'].zcard(listKey);
        entityCounts[schema.name] = count;
        totalEntities += count;
      }
      
      // Determine health status
      let status: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';
      if (schemas.length === 0) {
        status = 'unhealthy';
      } else if (executedMigrations.length < this.migrations.length) {
        status = 'degraded';
      }
      
      return {
        status,
        details: {
          totalEntities,
          totalSchemas: schemas.length,
          executedMigrations: executedMigrations.length,
          indexHealth: entityCounts
        }
      };
    } catch (error) {
      this.logger.error('Health check failed:', error);
      return {
        status: 'unhealthy',
        details: {
          totalEntities: 0,
          totalSchemas: 0,
          executedMigrations: 0,
          indexHealth: {}
        }
      };
    }
  }

  // Cleanup and maintenance
  async cleanupExpiredEntities(): Promise<number> {
    let cleaned = 0;
    
    try {
      this.logger.log('Starting cleanup of expired entities...');
      
      for (const schema of this.entitySchemas) {
        if (schema.ttl) {
          const pattern = `entity:${schema.name}:*`;
          const keys = await this.redisDb['redis'].keys(pattern);
          
          for (const key of keys) {
            const ttl = await this.redisDb['redis'].ttl(key);
            if (ttl === -1) {
              // Key exists but has no TTL, set it
              await this.redisDb['redis'].expire(key, schema.ttl);
            } else if (ttl === -2) {
              // Key doesn't exist (already expired and deleted)
              cleaned++;
            }
          }
        }
      }
      
      this.logger.log(`Cleanup completed. ${cleaned} entities were processed.`);
      return cleaned;
    } catch (error) {
      this.logger.error('Cleanup failed:', error);
      return cleaned;
    }
  }
}
