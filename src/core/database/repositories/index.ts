/**
 * Repository exports for Redis-based data access layer
 */

// Base repository
export * from './base-redis.repository';
export * from './base.repository';

// Repository factory
export * from './repository.factory';

// Specific entity repositories
export * from './user.repository';
export * from './session.repository';
export * from './guild.repository';
export * from './agent.repository';

// Repository types
export * from '../types/repository.types';

// Re-export commonly used base types
export type {
  BaseRedisRepository,
  ExtendedRedisRepository,
  RepositoryFactory,
  EntityKeyGenerator,
  RepositoryOptions,
  BulkOperation,
  EntityEvent,
  ExportOptions,
  ImportOptions,
  QueryBuilder,
  ComparisonOperator,
  QueryableRepository,
  MigrationRepository,
  HealthCheckRepository,
  RepositoryManager,
} from '../types/repository.types';

// Repository utility functions
export const RepositoryUtils = {
  /**
   * Create a key generator for an entity
   */
  createKeyGenerator: <T extends import('../types/base.interface').BaseEntity>(
    entityName: string,
    indexFields?: Array<keyof T>
  ): import('../types/base.interface').EntityKeyGenerator<T> => {
    const entityKey = entityName.toLowerCase();
    
    const generator: import('../types/base.interface').EntityKeyGenerator<T> = {
      primary: (id: string) => `${entityKey}:${id}`,
      pattern: `${entityKey}:*`,
      index: {} as Partial<Record<keyof T, string>>,
      search: `search:${entityKey}`,
      byField: (field: any, value: any) => `${entityKey}:${String(field)}:${value}`,
    };

    // Add index patterns for specified fields
    if (indexFields) {
      for (const field of indexFields) {
        generator.index[field] = `idx:${entityKey}:${String(field)}`;
      }
    }

    return generator;
  },

  /**
   * Create default repository options
   */
  createDefaultOptions: (overrides: Partial<import('../types/repository.types').RepositoryOptions> = {}): import('../types/repository.types').RepositoryOptions => ({
    enableCaching: false,
    defaultTTL: 3600,
    enableSoftDelete: false,
    enableVersioning: false,
    enableAudit: false,
    compressionEnabled: false,
    serializationStrategy: 'json',
    indexFields: [],
    searchFields: [],
    ...overrides,
  }),

  /**
   * Validate repository options
   */
  validateOptions: (options: import('../types/repository.types').RepositoryOptions): boolean => {
    if (options.defaultTTL && options.defaultTTL < 0) {
      throw new Error('Default TTL must be positive');
    }

    if (options.serializationStrategy && 
        !['json', 'msgpack', 'protobuf'].includes(options.serializationStrategy)) {
      throw new Error('Invalid serialization strategy');
    }

    return true;
  },
};

/**
 * Repository factory for creating repository instances
 */
import type { RepositoryFactory } from '../types/repository.types';

export class RedisRepositoryFactory implements RepositoryFactory {
  constructor(private readonly redisClient: import('ioredis').Redis) {}

  /**
   * Create a basic repository instance
   */
  create<T extends import('../types/base.interface').BaseEntity>(
    entityName: string,
    keyGenerator: import('../types/base.interface').EntityKeyGenerator<T>,
    options: import('../types/repository.types').RepositoryOptions = {}
  ): import('../types/repository.types').BaseRedisRepository<T> {
    // Implementation would go here - placeholder for now
    throw new Error('Repository factory not fully implemented yet');
  }

  /**
   * Create an extended repository instance with additional features
   */
  createExtended<T extends import('../types/base.interface').BaseEntity>(
    entityName: string,
    keyGenerator: import('../types/base.interface').EntityKeyGenerator<T>,
    options: import('../types/repository.types').RepositoryOptions = {}
  ): import('../types/repository.types').ExtendedRedisRepository<T> {
    // This would be implemented with additional features like caching, bulk operations, etc.
    throw new Error('Extended repository implementation not yet available');
  }

  private get redis() {
    return this.redisClient;
  }
}