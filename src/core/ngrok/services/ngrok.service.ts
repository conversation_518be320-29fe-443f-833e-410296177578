import { Injectable, OnModuleInit, OnModuleDestroy, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import ngrok from '@ngrok/ngrok';
import * as ngrokCommunity from 'ngrok';
import {
  INgrokService,
  TunnelConfig,
  TunnelStatus,
  NgrokHealthResult,
  NgrokServiceConfig,
  TunnelEvent,
  NgrokError,
  TunnelError,
  AuthenticationError,
  ConfigurationError,
  NgrokServiceMetrics,
  TunnelHealthStatus,
  DEFAULT_NGROK_CONFIG,
} from '../interfaces/ngrok.interface';

interface ActiveTunnel {
  id: string;
  config: TunnelConfig;
  listener?: any;
  url?: string;
  status: TunnelStatus['status'];
  startedAt: Date;
  retryCount: number;
  lastError?: string;
  healthCheckFailures: number;
}

@Injectable()
export class NgrokService implements INgrokService, OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(NgrokService.name);
  private readonly config: NgrokServiceConfig;
  private readonly activeTunnels = new Map<string, ActiveTunnel>();
  private isInitialized = false;
  private healthCheckInterval?: NodeJS.Timeout;
  private readonly events: TunnelEvent[] = [];
  private readonly maxEvents = 1000;

  constructor(
    private readonly configService: ConfigService,
    private readonly eventEmitter: EventEmitter2,
  ) {
    this.config = this.loadConfiguration();
  }

  async onModuleInit(): Promise<void> {
    if (this.config.enabled) {
      await this.initialize();
    }
  }

  async onModuleDestroy(): Promise<void> {
    await this.cleanup();
  }

  private loadConfiguration(): NgrokServiceConfig {
    return {
      ...DEFAULT_NGROK_CONFIG,
      enabled: this.configService.get<boolean>('NGROK_ENABLED', false),
      authToken: this.configService.get<string>('NGROK_AUTHTOKEN', ''),
      apiKey: this.configService.get<string>('NGROK_API_KEY'),
      region: this.configService.get('NGROK_REGION', 'us') as any,
      environment: this.configService.get('NODE_ENV', 'development') as any,
      retryAttempts: this.configService.get<number>('NGROK_RETRY_ATTEMPTS', 3),
      retryDelay: this.configService.get<number>('NGROK_RETRY_DELAY', 5000),
      healthCheckInterval: this.configService.get<number>('NGROK_HEALTH_CHECK_INTERVAL', 30000),
      autoRestart: this.configService.get<boolean>('NGROK_AUTO_RESTART', true),
      persistTunnels: this.configService.get<boolean>('NGROK_PERSIST_TUNNELS', true),
      logLevel: this.configService.get('NGROK_LOG_LEVEL', 'info') as any,
      defaultTunnel: this.parseDefaultTunnelConfig(),
      tunnels: this.parseTunnelConfigs(),
    } as NgrokServiceConfig;
  }

  private parseDefaultTunnelConfig(): TunnelConfig | undefined {
    const port = this.configService.get<number>('PORT', 3000);
    const domain = this.configService.get<string>('NGROK_DOMAIN');
    const subdomain = this.configService.get<string>('NGROK_SUBDOMAIN');

    if (!port) return undefined;

    return {
      id: 'default',
      proto: 'http',
      addr: port,
      domain,
      subdomain,
      region: this.config.region,
      basicAuth: this.configService.get<string>('NGROK_BASIC_AUTH'),
      oauthProvider: this.configService.get('NGROK_OAUTH_PROVIDER') as any,
      ipRestrictions: this.configService.get<string>('NGROK_IP_RESTRICTIONS', '')
        .split(',').filter((ip: any) => ip.trim()),
    };
  }

  private parseTunnelConfigs(): TunnelConfig[] {
    const tunnelsConfig = this.configService.get<string>('NGROK_TUNNELS_CONFIG');
    if (!tunnelsConfig) return [];

    try {
      return JSON.parse(tunnelsConfig);
    } catch (error) {
      this.logger.warn('Failed to parse NGROK_TUNNELS_CONFIG, using empty array');
      return [];
    }
  }

  async initialize(): Promise<void> {
    if (this.isInitialized) {
      this.logger.warn('NgrokService is already initialized');
      return;
    }

    if (!this.config.authToken) {
      throw new AuthenticationError('NGROK_AUTHTOKEN is required when ngrok is enabled');
    }

    this.logger.log('Initializing NgrokService...');

    try {
      // Initialize default tunnel if configured
      if (this.config.defaultTunnel) {
        await this.createTunnel(this.config.defaultTunnel);
      }

      // Initialize additional tunnels
      for (const tunnelConfig of this.config.tunnels) {
        await this.createTunnel(tunnelConfig);
      }

      // Start health monitoring
      this.startHealthMonitoring();

      this.isInitialized = true;
      this.logger.log('NgrokService initialized successfully');

      this.emitEvent({
        id: this.generateEventId(),
        tunnelId: 'system',
        type: 'started',
        timestamp: new Date(),
        message: 'NgrokService initialized',
        metadata: { 
          tunnelCount: this.activeTunnels.size,
          config: this.config 
        },
      });

    } catch (error) {
      this.logger.error('Failed to initialize NgrokService', error);
      throw error;
    }
  }

  async createTunnel(config: TunnelConfig): Promise<string> {
    if (this.activeTunnels.has(config.id)) {
      throw new ConfigurationError(`Tunnel '${config.id}' already exists`);
    }

    this.logger.log(`Creating tunnel '${config.id}' on ${config.proto}://${config.addr}`);

    const tunnel: ActiveTunnel = {
      id: config.id,
      config,
      status: 'connecting',
      startedAt: new Date(),
      retryCount: 0,
      healthCheckFailures: 0,
    };

    this.activeTunnels.set(config.id, tunnel);

    try {
      const url = await this.createTunnelWithRetry(tunnel);
      tunnel.url = url;
      tunnel.status = 'connected';

      this.logger.log(`Tunnel '${config.id}' established at: ${url}`);

      this.emitEvent({
        id: this.generateEventId(),
        tunnelId: config.id,
        type: 'started',
        timestamp: new Date(),
        message: `Tunnel started at ${url}`,
        metadata: { config, url },
      });

      return url;

    } catch (error) {
      tunnel.status = 'error';
      tunnel.lastError = (error as Error).message;
      
      this.logger.error(`Failed to create tunnel '${config.id}'`, error);
      
      this.emitEvent({
        id: this.generateEventId(),
        tunnelId: config.id,
        type: 'error',
        timestamp: new Date(),
        message: `Failed to create tunnel: ${(error as Error).message}`,
        metadata: { config, error: (error as Error).message },
      });

      throw new TunnelError((error as Error).message, config.id);
    }
  }

  private async createTunnelWithRetry(
    tunnel: ActiveTunnel,
    attempt: number = 1
  ): Promise<string> {
    try {
      const { config } = tunnel;
      
      // Use official SDK for HTTP/HTTPS tunnels
      if (config.proto === 'http' || config.proto === 'https') {
        const options: any = {
          addr: config.addr,
          authtoken: this.config.authToken,
          region: config.region,
        };

        if (config.domain) options.domain = config.domain;
        if (config.subdomain) options.subdomain = config.subdomain;
        if (config.basicAuth) options.basic_auth = config.basicAuth;
        if (config.oauthProvider) options.oauth_provider = config.oauthProvider;
        if (config.oauthAllowDomains?.length) options.oauth_allow_domains = config.oauthAllowDomains;
        if (config.ipRestrictions?.length) options.ip_restrictions = config.ipRestrictions;
        if (config.circuitBreaker) options.circuit_breaker = config.circuitBreaker;
        if (config.compression !== undefined) options.compression = config.compression;
        if (config.websocketTcpConverter) options.websocket_tcp_converter = config.websocketTcpConverter;

        const listener = await ngrok.forward(options);
        tunnel.listener = listener;
        return listener.url();
      }

      // Use community wrapper for TCP/TLS tunnels
      else if (config.proto === 'tcp' || config.proto === 'tls') {
        const options: any = {
          proto: config.proto,
          addr: config.addr,
          authtoken: this.config.authToken,
          region: config.region,
        };

        if (config.subdomain) options.subdomain = config.subdomain;

        const url = await ngrokCommunity.connect(options);
        tunnel.listener = { url, close: () => ngrokCommunity.disconnect(url) };
        return url;
      }

      else {
        throw new Error(`Unsupported protocol: ${config.proto}`);
      }

    } catch (error) {
      if (attempt < this.config.retryAttempts) {
        this.logger.warn(
          `Tunnel '${tunnel.id}' creation attempt ${attempt} failed, retrying in ${this.config.retryDelay}ms...`
        );
        
        tunnel.retryCount = attempt;
        tunnel.status = 'retrying';
        
        await new Promise(resolve => setTimeout(resolve, this.config.retryDelay));
        return this.createTunnelWithRetry(tunnel, attempt + 1);
      }
      
      throw error;
    }
  }

  async closeTunnel(tunnelId: string): Promise<void> {
    const tunnel = this.activeTunnels.get(tunnelId);
    if (!tunnel) {
      throw new ConfigurationError(`Tunnel '${tunnelId}' not found`);
    }

    this.logger.log(`Closing tunnel '${tunnelId}'`);

    try {
      if (tunnel.listener?.close) {
        await tunnel.listener.close();
      }

      this.activeTunnels.delete(tunnelId);

      this.emitEvent({
        id: this.generateEventId(),
        tunnelId,
        type: 'stopped',
        timestamp: new Date(),
        message: 'Tunnel closed',
      });

      this.logger.log(`Tunnel '${tunnelId}' closed successfully`);

    } catch (error) {
      this.logger.error(`Failed to close tunnel '${tunnelId}'`, error);
      throw new TunnelError((error as Error).message, tunnelId);
    }
  }

  async closeAllTunnels(): Promise<void> {
    this.logger.log('Closing all tunnels...');

    const closePromises = Array.from(this.activeTunnels.keys()).map(
      tunnelId => this.closeTunnel(tunnelId).catch(error => 
        this.logger.error(`Failed to close tunnel '${tunnelId}'`, error)
      )
    );

    await Promise.allSettled(closePromises);
    this.logger.log('All tunnels closed');
  }

  async getTunnelStatus(tunnelId: string): Promise<TunnelStatus | null> {
    const tunnel = this.activeTunnels.get(tunnelId);
    if (!tunnel) return null;

    const uptime = Date.now() - tunnel.startedAt.getTime();

    return {
      id: tunnel.id,
      status: tunnel.status,
      url: tunnel.url,
      proto: tunnel.config.proto,
      addr: tunnel.config.addr,
      startedAt: tunnel.startedAt,
      lastHealthCheck: new Date(), // TODO: Track actual health check times
      metrics: await this.getTunnelMetrics(tunnelId),
      errors: tunnel.lastError ? [tunnel.lastError] : [],
    };
  }

  async getAllTunnelStatuses(): Promise<TunnelStatus[]> {
    const statuses: TunnelStatus[] = [];
    
    for (const tunnelId of this.activeTunnels.keys()) {
      const status = await this.getTunnelStatus(tunnelId);
      if (status) statuses.push(status);
    }

    return statuses;
  }

  async restartTunnel(tunnelId: string): Promise<string> {
    const tunnel = this.activeTunnels.get(tunnelId);
    if (!tunnel) {
      throw new ConfigurationError(`Tunnel '${tunnelId}' not found`);
    }

    this.logger.log(`Restarting tunnel '${tunnelId}'`);

    const config = tunnel.config;
    
    try {
      await this.closeTunnel(tunnelId);
      const url = await this.createTunnel(config);

      this.emitEvent({
        id: this.generateEventId(),
        tunnelId,
        type: 'reconnected',
        timestamp: new Date(),
        message: `Tunnel restarted at ${url}`,
        metadata: { url },
      });

      return url;

    } catch (error) {
      this.logger.error(`Failed to restart tunnel '${tunnelId}'`, error);
      throw new TunnelError((error as Error).message, tunnelId);
    }
  }

  async getHealthStatus(): Promise<NgrokHealthResult> {
    const tunnels: TunnelHealthStatus[] = [];
    const errors: string[] = [];
    let healthyCount = 0;
    let warningCount = 0;
    let criticalCount = 0;

    for (const [tunnelId, tunnel] of this.activeTunnels) {
      const uptime = Date.now() - tunnel.startedAt.getTime();
      
      let status: 'healthy' | 'warning' | 'critical';
      
      if (tunnel.status === 'connected' && tunnel.healthCheckFailures === 0) {
        status = 'healthy';
        healthyCount++;
      } else if (tunnel.status === 'connecting' || tunnel.healthCheckFailures < 3) {
        status = 'warning';
        warningCount++;
      } else {
        status = 'critical';
        criticalCount++;
        if (tunnel.lastError) {
          errors.push(`Tunnel '${tunnelId}': ${tunnel.lastError}`);
        }
      }

      tunnels.push({
        id: tunnelId,
        status,
        url: tunnel.url,
        uptime,
        lastError: tunnel.lastError,
      });
    }

    const overall = criticalCount > 0 ? 'critical' : 
                   warningCount > 0 ? 'warning' : 'healthy';

    const metrics: NgrokServiceMetrics = {
      tunnelsTotal: this.activeTunnels.size,
      tunnelsActive: healthyCount,
      tunnelsConnecting: warningCount,
      tunnelsError: criticalCount,
      totalUptime: this.isInitialized ? Date.now() - Date.now() : 0, // TODO: Track service start time
      restartCount: 0, // TODO: Track restart count
      avgResponseTime: 0, // TODO: Implement response time tracking
      totalRequests: 0, // TODO: Implement request counting
      errorRate: criticalCount / Math.max(this.activeTunnels.size, 1),
    };

    return {
      overall,
      tunnels,
      metrics,
      lastCheck: new Date(),
      errors,
    };
  }

  private async getTunnelMetrics(tunnelId: string): Promise<any> {
    // TODO: Implement actual metrics collection
    // This would require integration with ngrok's management API
    return {
      connectionsTotal: 0,
      connectionsActive: 0,
      bytesIn: 0,
      bytesOut: 0,
      requestsPerSecond: 0,
      avgResponseTime: 0,
      errorRate: 0,
      uptime: 0,
    };
  }

  private startHealthMonitoring(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }

    this.healthCheckInterval = setInterval(
      async () => {
        try {
          await this.performHealthChecks();
        } catch (error) {
          this.logger.error('Health check failed', error);
        }
      },
      this.config.healthCheckInterval
    );

    this.logger.debug(`Health monitoring started (interval: ${this.config.healthCheckInterval}ms)`);
  }

  private async performHealthChecks(): Promise<void> {
    for (const [tunnelId, tunnel] of this.activeTunnels) {
      try {
        if (tunnel.status === 'connected' && tunnel.url) {
          // Perform basic connectivity check
          const isHealthy = await this.checkTunnelConnectivity(tunnel.url);
          
          if (isHealthy) {
            tunnel.healthCheckFailures = 0;
          } else {
            tunnel.healthCheckFailures++;
            
            if (tunnel.healthCheckFailures >= 3 && this.config.autoRestart) {
              this.logger.warn(`Tunnel '${tunnelId}' failed health checks, attempting restart...`);
              try {
                await this.restartTunnel(tunnelId);
              } catch (error) {
                this.logger.error(`Failed to restart tunnel '${tunnelId}'`, error);
              }
            }
          }
        }
      } catch (error) {
        this.logger.error(`Health check failed for tunnel '${tunnelId}'`, error);
        tunnel.healthCheckFailures++;
      }
    }

    this.emitEvent({
      id: this.generateEventId(),
      tunnelId: 'system',
      type: 'health_check',
      timestamp: new Date(),
      message: 'Health check completed',
      metadata: await this.getHealthStatus(),
    });
  }

  private async checkTunnelConnectivity(url: string): Promise<boolean> {
    try {
      // Simple HTTP check to verify tunnel is responding
      const response = await fetch(`${url}/health`, {
        method: 'GET',
        signal: AbortSignal.timeout(5000),
      });
      
      return response.status < 500;
    } catch (error) {
      return false;
    }
  }

  private emitEvent(event: TunnelEvent): void {
    this.events.unshift(event);
    
    // Keep only the most recent events
    if (this.events.length > this.maxEvents) {
      this.events.splice(this.maxEvents);
    }

    // Emit to event system
    this.eventEmitter.emit('ngrok.tunnel.event', event);
    
    // Log based on type
    switch (event.type) {
      case 'error':
        this.logger.error(`Tunnel event: ${event.message}`, event.metadata);
        break;
      case 'started':
      case 'stopped':
      case 'reconnected':
        this.logger.log(`Tunnel event: ${event.message}`);
        break;
      default:
        this.logger.debug(`Tunnel event: ${event.message}`);
    }
  }

  private generateEventId(): string {
    return `evt_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
  }

  private async cleanup(): Promise<void> {
    this.logger.log('Cleaning up NgrokService...');

    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }

    await this.closeAllTunnels();
    
    this.isInitialized = false;
    this.logger.log('NgrokService cleanup complete');
  }

  isEnabled(): boolean {
    return this.config.enabled && this.isInitialized;
  }

  getConfig(): NgrokServiceConfig {
    return { ...this.config };
  }

  getEvents(limit: number = 100): TunnelEvent[] {
    return this.events.slice(0, limit);
  }

  getTunnelUrl(tunnelId: string): string | null {
    const tunnel = this.activeTunnels.get(tunnelId);
    return tunnel?.url || null;
  }

  getAllTunnelUrls(): Record<string, string> {
    const urls: Record<string, string> = {};
    
    for (const [id, tunnel] of this.activeTunnels) {
      if (tunnel.url) {
        urls[id] = tunnel.url;
      }
    }
    
    return urls;
  }
}
