import { Injectable, Logger, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { <PERSON>ron, CronExpression } from '@nestjs/schedule';
import * as fs from 'fs/promises';
import * as path from 'path';
import {
  TunnelConfig,
  TunnelStatus,
  TunnelEvent,
  NgrokDiscordIntegration,
} from '../interfaces/ngrok.interface';
import { NgrokService } from './ngrok.service';
import { NgrokDiscordService } from './ngrok-discord.service';

interface TunnelPersistenceData {
  id: string;
  config: TunnelConfig;
  url?: string;
  startedAt: Date;
  isAutoRestart: boolean;
  metadata?: Record<string, any>;
}

interface NgrokPersistenceState {
  version: string;
  savedAt: Date;
  tunnels: TunnelPersistenceData[];
  discordIntegration?: NgrokDiscordIntegration;
  serviceConfig: {
    environment: string;
    region: string;
    autoRestart: boolean;
  };
}

interface PersistenceConfig {
  enabled: boolean;
  statePath: string;
  backupInterval: number;
  autoRecover: boolean;
  maxRecoveryAttempts: number;
  retainBackups: number;
  compressionEnabled: boolean;
}

@Injectable()
export class NgrokPersistenceService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(NgrokPersistenceService.name);
  private readonly config: PersistenceConfig;
  private readonly persistenceVersion = '1.0.0';
  private backupInterval?: NodeJS.Timeout;
  private isInitialized = false;
  private recoveryAttempts = 0;

  constructor(
    private readonly configService: ConfigService,
    private readonly eventEmitter: EventEmitter2,
    private readonly ngrokService: NgrokService,
    private readonly ngrokDiscordService: NgrokDiscordService,
  ) {
    this.config = this.loadConfiguration();
  }

  async onModuleInit(): Promise<void> {
    if (this.config.enabled) {
      await this.initialize();
    }
  }

  async onModuleDestroy(): Promise<void> {
    await this.cleanup();
  }

  private loadConfiguration(): PersistenceConfig {
    return {
      enabled: this.configService.get<boolean>('NGROK_PERSISTENCE_ENABLED', true),
      statePath: this.configService.get<string>('NGROK_STATE_PATH', './data/ngrok-state.json'),
      backupInterval: this.configService.get<number>('NGROK_BACKUP_INTERVAL', 300000), // 5 minutes
      autoRecover: this.configService.get<boolean>('NGROK_AUTO_RECOVER', true),
      maxRecoveryAttempts: this.configService.get<number>('NGROK_MAX_RECOVERY_ATTEMPTS', 5),
      retainBackups: this.configService.get<number>('NGROK_RETAIN_BACKUPS', 10),
      compressionEnabled: this.configService.get<boolean>('NGROK_PERSISTENCE_COMPRESSION', false),
    };
  }

  private async initialize(): Promise<void> {
    if (this.isInitialized) {
      this.logger.warn('Persistence service is already initialized');
      return;
    }

    this.logger.log('Initializing ngrok persistence service...');

    try {
      // Ensure state directory exists
      await this.ensureStateDirectory();

      // Attempt recovery if enabled
      if (this.config.autoRecover) {
        await this.recoverTunnels();
      }

      // Start periodic backup
      this.startPeriodicBackup();

      // Listen for tunnel events
      this.eventEmitter.on('ngrok.tunnel.event', this.handleTunnelEvent.bind(this));
      this.eventEmitter.on('discord.webhook.urls.updated', this.handleDiscordUpdate.bind(this));

      this.isInitialized = true;
      this.logger.log('Persistence service initialized successfully');

    } catch (error) {
      this.logger.error('Failed to initialize persistence service', error);
      throw error;
    }
  }

  private async cleanup(): Promise<void> {
    if (!this.isInitialized) {
      return;
    }

    this.logger.log('Cleaning up persistence service...');

    try {
      // Save final state
      await this.saveState();

      // Stop periodic backup
      if (this.backupInterval) {
        clearInterval(this.backupInterval);
        this.backupInterval = undefined;
      }

      // Remove event listeners
      this.eventEmitter.off('ngrok.tunnel.event', this.handleTunnelEvent.bind(this));
      this.eventEmitter.off('discord.webhook.urls.updated', this.handleDiscordUpdate.bind(this));

      this.isInitialized = false;
      this.logger.log('Persistence service cleanup complete');

    } catch (error) {
      this.logger.error('Error during persistence service cleanup', error);
    }
  }

  async saveState(): Promise<void> {
    if (!this.config.enabled) {
      return;
    }

    try {
      const state = await this.collectCurrentState();
      await this.writeStateToFile(state);
      
      this.logger.debug('Tunnel state saved successfully');

      this.eventEmitter.emit('ngrok.persistence.state.saved', {
        tunnelsCount: state.tunnels.length,
        timestamp: new Date(),
      });

    } catch (error) {
      this.logger.error('Failed to save tunnel state', error);
      
      this.eventEmitter.emit('ngrok.persistence.state.save.failed', {
        error: (error as Error).message,
        timestamp: new Date(),
      });
      
      throw error;
    }
  }

  async loadState(): Promise<NgrokPersistenceState | null> {
    if (!this.config.enabled) {
      return null;
    }

    try {
      const exists = await this.fileExists(this.config.statePath);
      if (!exists) {
        this.logger.debug('No state file found, starting fresh');
        return null;
      }

      const state = await this.readStateFromFile();
      
      this.logger.debug(`Loaded state with ${state.tunnels.length} tunnels`);
      return state;

    } catch (error) {
      this.logger.error('Failed to load tunnel state', error);
      throw error;
    }
  }

  async recoverTunnels(): Promise<{
    recovered: number;
    failed: number;
    errors: string[];
  }> {
    if (!this.config.autoRecover) {
      throw new Error('Auto-recovery is disabled');
    }

    this.logger.log('Attempting to recover tunnels from persistent state...');

    const result = {
      recovered: 0,
      failed: 0,
      errors: [] as string[],
    };

    try {
      const state = await this.loadState();
      if (!state || state.tunnels.length === 0) {
        this.logger.log('No tunnels to recover');
        return result;
      }

      this.logger.log(`Found ${state.tunnels.length} tunnels to recover`);

      // Check if we've exceeded max recovery attempts
      if (this.recoveryAttempts >= this.config.maxRecoveryAttempts) {
        throw new Error(`Maximum recovery attempts (${this.config.maxRecoveryAttempts}) exceeded`);
      }

      this.recoveryAttempts++;

      // Attempt to recover each tunnel
      for (const tunnelData of state.tunnels) {
        try {
          await this.recoverSingleTunnel(tunnelData);
          result.recovered++;
          
          this.logger.log(`Successfully recovered tunnel: ${tunnelData.id}`);

        } catch (error) {
          result.failed++;
          result.errors.push(`${tunnelData.id}: ${(error as Error).message}`);
          
          this.logger.error(`Failed to recover tunnel ${tunnelData.id}`, error);
        }
      }

      // Attempt to recover Discord integration
      if (state.discordIntegration) {
        try {
          await this.recoverDiscordIntegration(state.discordIntegration);
          this.logger.log('Discord integration recovered successfully');
        } catch (error) {
          this.logger.error('Failed to recover Discord integration', error);
          result.errors.push(`Discord integration: ${(error as Error).message}`);
        }
      }

      this.logger.log(`Recovery completed: ${result.recovered} recovered, ${result.failed} failed`);

      this.eventEmitter.emit('ngrok.persistence.recovery.completed', {
        ...result,
        recoveryAttempt: this.recoveryAttempts,
        timestamp: new Date(),
      });

      return result;

    } catch (error) {
      this.logger.error('Tunnel recovery failed', error);
      
      this.eventEmitter.emit('ngrok.persistence.recovery.failed', {
        error: (error as Error).message,
        recoveryAttempt: this.recoveryAttempts,
        timestamp: new Date(),
      });
      
      throw error;
    }
  }

  private async recoverSingleTunnel(tunnelData: TunnelPersistenceData): Promise<void> {
    // Check if tunnel already exists
    const existingTunnel = await this.ngrokService.getTunnelStatus(tunnelData.id);
    if (existingTunnel && existingTunnel.status === 'connected') {
      this.logger.debug(`Tunnel ${tunnelData.id} already exists and is connected`);
      return;
    }

    // Create the tunnel
    await this.ngrokService.createTunnel(tunnelData.config);
    
    this.logger.debug(`Tunnel ${tunnelData.id} recovered successfully`);
  }

  private async recoverDiscordIntegration(integration: NgrokDiscordIntegration): Promise<void> {
    // Check if Discord integration is already active
    const currentIntegration = this.ngrokDiscordService.getDiscordIntegrationStatus();
    if (currentIntegration && currentIntegration.webhookUrl) {
      this.logger.debug('Discord integration already active');
      return;
    }

    // Attempt to restore Discord integration
    await this.ngrokDiscordService.setupDiscordWebhooks();
  }

  private async collectCurrentState(): Promise<NgrokPersistenceState> {
    const tunnelStatuses = await this.ngrokService.getAllTunnelStatuses();
    const discordIntegration = this.ngrokDiscordService.getDiscordIntegrationStatus();
    const serviceConfig = this.ngrokService.getConfig();

    const tunnels: TunnelPersistenceData[] = tunnelStatuses.map((status: any) => ({
      id: status.id,
      config: {
        id: status.id,
        proto: status.proto as any,
        addr: status.addr,
      },
      url: status.url,
      startedAt: status.startedAt || new Date(),
      isAutoRestart: true, // Assume auto-restart for persistent tunnels
      metadata: {
        lastHealthCheck: status.lastHealthCheck,
        metrics: status.metrics,
      },
    }));

    return {
      version: this.persistenceVersion,
      savedAt: new Date(),
      tunnels,
      discordIntegration: discordIntegration || undefined,
      serviceConfig: {
        environment: serviceConfig.environment,
        region: serviceConfig.region,
        autoRestart: serviceConfig.autoRestart,
      },
    };
  }

  private async writeStateToFile(state: NgrokPersistenceState): Promise<void> {
    const stateData = JSON.stringify(state, null, 2);
    
    // Create backup of existing state
    await this.createBackup();
    
    // Write new state
    await fs.writeFile(this.config.statePath, stateData, 'utf8');
    
    this.logger.debug(`State written to ${this.config.statePath}`);
  }

  private async readStateFromFile(): Promise<NgrokPersistenceState> {
    const stateData = await fs.readFile(this.config.statePath, 'utf8');
    const state = JSON.parse(stateData) as NgrokPersistenceState;
    
    // Validate state version
    if (state.version !== this.persistenceVersion) {
      this.logger.warn(`State version mismatch: ${state.version} vs ${this.persistenceVersion}`);
      // Could implement migration logic here if needed
    }
    
    return state;
  }

  private async createBackup(): Promise<void> {
    try {
      const exists = await this.fileExists(this.config.statePath);
      if (!exists) {
        return;
      }

      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const backupPath = `${this.config.statePath}.backup.${timestamp}`;
      
      await fs.copyFile(this.config.statePath, backupPath);
      
      // Clean up old backups
      await this.cleanupOldBackups();
      
      this.logger.debug(`Backup created: ${backupPath}`);

    } catch (error) {
      this.logger.warn('Failed to create backup', error);
      // Don't throw - backup failure shouldn't prevent state saving
    }
  }

  private async cleanupOldBackups(): Promise<void> {
    try {
      const stateDir = path.dirname(this.config.statePath);
      const stateFileName = path.basename(this.config.statePath);
      
      const files = await fs.readdir(stateDir);
      const backupFiles = files
        .filter((file: any) => file.startsWith(`${stateFileName}.backup.`))
        .map((file: any) => ({
          name: file,
          path: path.join(stateDir, file),
        }))
        .sort((a, b) => b.name.localeCompare(a.name)); // Sort by name (newest first)

      if (backupFiles.length > this.config.retainBackups) {
        const filesToDelete = backupFiles.slice(this.config.retainBackups);
        
        for (const file of filesToDelete) {
          await fs.unlink(file.path);
          this.logger.debug(`Deleted old backup: ${file.name}`);
        }
      }

    } catch (error) {
      this.logger.warn('Failed to cleanup old backups', error);
    }
  }

  private async ensureStateDirectory(): Promise<void> {
    const stateDir = path.dirname(this.config.statePath);
    
    try {
      await fs.access(stateDir);
    } catch (error) {
      // Directory doesn't exist, create it
      await fs.mkdir(stateDir, { recursive: true });
      this.logger.log(`Created state directory: ${stateDir}`);
    }
  }

  private async fileExists(filePath: string): Promise<boolean> {
    try {
      await fs.access(filePath);
      return true;
    } catch {
      return false;
    }
  }

  private startPeriodicBackup(): void {
    if (this.backupInterval) {
      clearInterval(this.backupInterval);
    }

    this.backupInterval = setInterval(
      async () => {
        try {
          await this.saveState();
        } catch (error) {
          this.logger.error('Periodic backup failed', error);
        }
      },
      this.config.backupInterval
    );

    this.logger.debug(`Periodic backup started (interval: ${this.config.backupInterval}ms)`);
  }

  private handleTunnelEvent(event: TunnelEvent): void {
    // Save state on significant tunnel events
    if (['started', 'stopped', 'reconnected'].includes(event.type)) {
      setTimeout(() => {
        this.saveState().catch(error => 
          this.logger.error('Failed to save state after tunnel event', error)
        );
      }, 1000); // Small delay to ensure state is consistent
    }
  }

  private handleDiscordUpdate(integration: NgrokDiscordIntegration): void {
    // Save state when Discord integration is updated
    setTimeout(() => {
      this.saveState().catch(error => 
        this.logger.error('Failed to save state after Discord update', error)
      );
    }, 1000);
  }

  // Scheduled backup
  @Cron(CronExpression.EVERY_5_MINUTES)
  async scheduledBackup(): Promise<void> {
    if (this.config.enabled && this.isInitialized) {
      try {
        await this.saveState();
      } catch (error) {
        this.logger.error('Scheduled backup failed', error);
      }
    }
  }

  // Cleanup old data
  @Cron(CronExpression.EVERY_HOUR)
  async cleanupOldData(): Promise<void> {
    if (this.config.enabled) {
      await this.cleanupOldBackups();
    }
  }

  // Public methods for manual control
  async forceBackup(): Promise<void> {
    await this.saveState();
  }

  async restoreFromBackup(backupPath?: string): Promise<{
    recovered: number;
    failed: number;
    errors: string[];
  }> {
    if (backupPath) {
      // Temporarily use the specified backup file
      const originalPath = this.config.statePath;
      this.config.statePath = backupPath;
      
      try {
        const result = await this.recoverTunnels();
        return result;
      } finally {
        this.config.statePath = originalPath;
      }
    } else {
      return await this.recoverTunnels();
    }
  }

  getBackupList(): Promise<string[]> {
    return this.listBackupFiles();
  }

  private async listBackupFiles(): Promise<string[]> {
    try {
      const stateDir = path.dirname(this.config.statePath);
      const stateFileName = path.basename(this.config.statePath);
      
      const files = await fs.readdir(stateDir);
      return files
        .filter((file: any) => file.startsWith(`${stateFileName}.backup.`))
        .sort((a, b) => b.localeCompare(a)); // Sort by name (newest first)

    } catch (error) {
      this.logger.error('Failed to list backup files', error);
      return [];
    }
  }

  isPersistenceEnabled(): boolean {
    return this.config.enabled;
  }

  getConfig(): PersistenceConfig {
    return { ...this.config };
  }

  getRecoveryAttempts(): number {
    return this.recoveryAttempts;
  }

  resetRecoveryAttempts(): void {
    this.recoveryAttempts = 0;
    this.logger.log('Recovery attempts counter reset');
  }
}
