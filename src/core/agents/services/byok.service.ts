import { Injectable, Logger, Inject } from '@nestjs/common';



import { createHash, createCipheriv, createDecipheriv, randomBytes } from 'crypto';

export interface UserApiKeys {
  memberId: string;
  keys: {
    openai?: EncryptedApiKey;
    anthropic?: EncryptedApiKey;
    google?: EncryptedApiKey;
    exa?: EncryptedApiKey;
  };
  usage: ApiUsageTracking;
  preferences: ApiPreferences;
}

export interface EncryptedApiKey {
  encryptedKey: string;
  provider: 'openai' | 'anthropic' | 'google' | 'exa';
  createdAt: Date;
  lastValidated: Date;
  isValid: boolean;
  availableModels: string[];
  usageLimits?: {
    dailyLimit?: number;
    monthlyLimit?: number;
    costLimit?: number;
  };
}

export interface ApiUsageTracking {
  daily: UsageMetrics;
  monthly: UsageMetrics;
  total: UsageMetrics;
  sessions: UsageSession[];
}

export interface UsageMetrics {
  requests: number;
  tokens: number;
  cost: number;
  period: string;
}

export interface UsageSession {
  sessionId: string;
  startTime: Date;
  endTime?: Date;
  requests: number;
  tokens: number;
  cost: number;
  agentType: string;
  models: string[];
}

export interface ApiPreferences {
  preferredProvider: 'openai' | 'anthropic' | 'google' | 'exa' | 'auto';
  preferredModels: {
    aiMastery: string;
    wealthCreation: string;
    personalGrowth: string;
    devSupport?: string;
  };
  costAlerts: {
    dailyThreshold: number;
    monthlyThreshold: number;
    enabled: boolean;
  };
  usageReports: {
    frequency: 'daily' | 'weekly' | 'monthly';
    enabled: boolean;
  };
}

export interface AIModel {
  id: string;
  provider: 'openai' | 'anthropic' | 'google' | 'exa';
  name: string;
  description: string;
  costPerToken: {
    input: number;
    output: number;
  };
  capabilities: string[];
  contextWindow: number;
  available: boolean;
}

@Injectable()
export class BYOKService {
  private readonly logger = new Logger(BYOKService.name);
  private readonly encryptionKey = process.env.BYOK_ENCRYPTION_KEY || 'default-key-change-me';
  private readonly memberKeys = new Map<string, UserApiKeys>();
  private readonly activeModels = new Map<string, AIModel[]>();

  constructor(
  ) {
    this.initializeAvailableModels();
  }

  async storeUserKey(memberId: string, provider: string, apiKey: string): Promise<void> {
    try {
      this.logger.log(`Storing API key for member ${memberId}, provider ${provider}`);

      // Validate the API key first
      const isValid = await this.validateKey(provider as any, apiKey);
      if (!isValid) {
        throw new Error(`Invalid API key for provider ${provider}`);
      }

      // Get available models for this key
      const availableModels = await this.getProviderAvailableModels(provider as any, apiKey);

      // Encrypt the API key
      const encryptedKey = this.encryptKey(apiKey);

      // Get or create user's key store
      let userKeys = this.memberKeys.get(memberId);
      if (!userKeys) {
        userKeys = await this.initializeUserKeys(memberId);
      }

      // Store encrypted key
      const keyData: EncryptedApiKey = {
        encryptedKey,
        provider: provider as any,
        createdAt: new Date(),
        lastValidated: new Date(),
        isValid: true,
        availableModels,
        usageLimits: {
          dailyLimit: 10000, // tokens
          monthlyLimit: 300000, // tokens
          costLimit: 100 // USD
        }
      };

      userKeys.keys[provider as keyof typeof userKeys.keys] = keyData;
      this.memberKeys.set(memberId, userKeys);

      // Persist to database
      await this.persistUserKeys(memberId, userKeys);

      this.logger.log(`Successfully stored API key for member ${memberId}, provider ${provider}`);
    } catch (error) {
      this.logger.error(`Failed to store API key for member ${memberId}:`, error);
      throw error;
    }
  }

  async getUserKeys(memberId: string): Promise<UserApiKeys> {
    try {
      let userKeys = this.memberKeys.get(memberId);
      
      if (!userKeys) {
        // Try loading from database
        const loadedKeys = await this.loadUserKeys(memberId);
        userKeys = loadedKeys ?? undefined;
        if (userKeys) {
          this.memberKeys.set(memberId, userKeys);
        } else {
          // Create new key store
          userKeys = await this.initializeUserKeys(memberId);
        }
      }

      return userKeys;
    } catch (error) {
      this.logger.error(`Failed to get user keys for member ${memberId}:`, error);
      throw error;
    }
  }

  async getUserKey(memberId: string, provider: 'openai' | 'anthropic' | 'google' | 'exa'): Promise<EncryptedApiKey | null> {
    try {
      const userKeys = await this.getUserKeys(memberId);
      return userKeys.keys[provider] || null;
    } catch (error) {
      this.logger.error(`Failed to get ${provider} key for member ${memberId}:`, error);
      return null;
    }
  }

  async validateKey(provider: 'openai' | 'anthropic' | 'google' | 'exa', apiKey: string): Promise<boolean> {
    try {
      switch (provider) {
        case 'openai':
          return await this.validateOpenAIKey(apiKey);
        case 'anthropic':
          return await this.validateAnthropicKey(apiKey);
        case 'google':
          return await this.validateGoogleKey(apiKey);
        case 'exa':
          return await this.validateExaKey(apiKey);
        default:
          return false;
      }
    } catch (error) {
      this.logger.error(`Failed to validate ${provider} key:`, error);
      return false;
    }
  }

  encryptKey(apiKey: string): string {
    try {
      const iv = randomBytes(16);
      const hash = createHash('sha256').update(this.encryptionKey).digest();
      const cipher = createCipheriv('aes-256-cbc', hash, iv);
      let encrypted = cipher.update(apiKey, 'utf8', 'hex');
      encrypted += cipher.final('hex');
      
      // Add IV to encrypted data
      return `${iv.toString('hex')}:${encrypted}`;
    } catch (error) {
      this.logger.error('Failed to encrypt API key:', (error as Error).message);
      throw error;
    }
  }

  decryptKey(encryptedKey: string): string {
    try {
      const [ivHex, encrypted] = encryptedKey.split(':');
      if (!encrypted || !ivHex) {
        throw new Error('Invalid encrypted key format');
      }
      const iv = Buffer.from(ivHex, 'hex');
      const hash = createHash('sha256').update(this.encryptionKey).digest();
      const decipher = createDecipheriv('aes-256-cbc', hash, iv);
      let decrypted: string = decipher.update(encrypted, 'hex', 'utf8');
      decrypted += decipher.final('utf8');
      
      // No integrity check needed with IV-based encryption
      
      return decrypted;
    } catch (error) {
      this.logger.error('Failed to decrypt API key:', (error as Error).message);
      throw error;
    }
  }

  async getAvailableModels(memberId: string): Promise<AIModel[]> {
    try {
      const userKeys = await this.getUserKeys(memberId);
      const availableModels: AIModel[] = [];

      // Get models for each provider the user has keys for
      for (const [provider, keyData] of Object.entries(userKeys.keys)) {
        if (keyData && keyData.isValid) {
          const providerModels = await this.getProviderModels(provider as any, keyData);
          availableModels.push(...providerModels);
        }
      }

      return availableModels;
    } catch (error) {
      this.logger.error(`Failed to get available models for member ${memberId}:`, error);
      return [];
    }
  }

  async selectBestModel(memberId: string, task: string): Promise<AIModel | null> {
    try {
      const availableModels = await this.getAvailableModels(memberId);
      const userKeys = await this.getUserKeys(memberId);
      
      if (availableModels.length === 0) {
        return null;
      }

      // Score models based on task and user preferences
      const scoredModels = availableModels.map((model: any) => ({
        model,
        score: this.calculateModelScore(model, task, userKeys.preferences)
      }));

      // Sort by score and return best model
      scoredModels.sort((a, b) => b.score - a.score);
      if (scoredModels.length === 0) {
        return null;
      }
      return scoredModels[0]?.model || null;
    } catch (error) {
      this.logger.error(`Failed to select best model for member ${memberId}:`, error);
      return null;
    }
  }

  async routeRequest(memberId: string, prompt: string, modelId: string): Promise<string> {
    try {
      this.logger.log(`Routing request for member ${memberId} using model ${modelId}`);

      // Get user's API keys
      const userKeys = await this.getUserKeys(memberId);
      
      // Find the model and associated key
      const model = await this.findModel(modelId);
      if (!model) {
        throw new Error(`Model ${modelId} not found`);
      }

      const keyData = userKeys.keys[model.provider];
      if (!keyData || !keyData.isValid) {
        throw new Error(`No valid API key for provider ${model.provider}`);
      }

      // Check usage limits
      await this.checkUsageLimits(memberId, userKeys);

      // Decrypt API key
      const apiKey = this.decryptKey(keyData.encryptedKey);

      // Start usage session
      const sessionId = await this.startUsageSession(memberId, model.provider, modelId);

      try {
        // Route to appropriate provider
        const response = await this.callProviderAPI(model.provider, modelId, prompt, apiKey);
        
        // Track usage
        await this.trackUsage(memberId, sessionId, response.usage);
        
        return response.content;
      } catch (apiError: unknown) {
        // End session with error
        const errorMessage = (apiError as Error).message;
        await this.endUsageSession(memberId, sessionId, { error: errorMessage });
        throw apiError;
      }
    } catch (error) {
      this.logger.error(`Failed to route request for member ${memberId}:`, error);
      throw error;
    }
  }

  async trackUsage(memberId: string, model: string, tokens: number): Promise<void> {
    try {
      const userKeys = await this.getUserKeys(memberId);
      const cost = await this.calculateCost(model, tokens);

      // Update daily usage
      userKeys.usage.daily.requests += 1;
      userKeys.usage.daily.tokens += tokens;
      userKeys.usage.daily.cost += cost;

      // Update monthly usage
      userKeys.usage.monthly.requests += 1;
      userKeys.usage.monthly.tokens += tokens;
      userKeys.usage.monthly.cost += cost;

      // Update total usage
      userKeys.usage.total.requests += 1;
      userKeys.usage.total.tokens += tokens;
      userKeys.usage.total.cost += cost;

      // Check for alerts
      await this.checkUsageAlerts(memberId, userKeys);

      // Update in memory and database
      this.memberKeys.set(memberId, userKeys);
      await this.persistUserKeys(memberId, userKeys);

      this.logger.log(`Tracked usage for member ${memberId}: ${tokens} tokens, $${cost.toFixed(4)}`);
    } catch (error) {
      this.logger.error(`Failed to track usage for member ${memberId}:`, error);
    }
  }

  async getUsageReport(memberId: string, period: 'daily' | 'monthly' | 'total'): Promise<UsageMetrics> {
    try {
      const userKeys = await this.getUserKeys(memberId);
      return userKeys.usage[period];
    } catch (error) {
      this.logger.error(`Failed to get usage report for member ${memberId}:`, error);
      throw error;
    }
  }

  async updatePreferences(memberId: string, preferences: Partial<ApiPreferences>): Promise<void> {
    try {
      const userKeys = await this.getUserKeys(memberId);
      userKeys.preferences = { ...userKeys.preferences, ...preferences };
      
      this.memberKeys.set(memberId, userKeys);
      await this.persistUserKeys(memberId, userKeys);
      
      this.logger.log(`Updated preferences for member ${memberId}`);
    } catch (error) {
      this.logger.error(`Failed to update preferences for member ${memberId}:`, error);
      throw error;
    }
  }

  async removeUserKey(memberId: string, provider: string): Promise<void> {
    try {
      const userKeys = await this.getUserKeys(memberId);
      delete userKeys.keys[provider as keyof typeof userKeys.keys];
      
      this.memberKeys.set(memberId, userKeys);
      await this.persistUserKeys(memberId, userKeys);
      
      this.logger.log(`Removed ${provider} key for member ${memberId}`);
    } catch (error) {
      this.logger.error(`Failed to remove key for member ${memberId}:`, error);
      throw error;
    }
  }

  private async initializeUserKeys(memberId: string): Promise<UserApiKeys> {
    const userKeys: UserApiKeys = {
      memberId,
      keys: {},
      usage: {
        daily: { requests: 0, tokens: 0, cost: 0, period: new Date().toDateString() },
        monthly: { requests: 0, tokens: 0, cost: 0, period: `${new Date().getFullYear()}-${new Date().getMonth() + 1}` },
        total: { requests: 0, tokens: 0, cost: 0, period: 'all-time' },
        sessions: []
      },
      preferences: {
        preferredProvider: 'auto',
        preferredModels: {
          aiMastery: 'gpt-4o-mini',
          wealthCreation: 'claude-3-haiku',
          personalGrowth: 'gpt-4o-mini'
        },
        costAlerts: {
          dailyThreshold: 10,
          monthlyThreshold: 100,
          enabled: true
        },
        usageReports: {
          frequency: 'weekly',
          enabled: true
        }
      }
    };

    this.memberKeys.set(memberId, userKeys);
    return userKeys;
  }

  private initializeAvailableModels(): void {
    const openaiModels: AIModel[] = [
      {
        id: 'gpt-4o',
        provider: 'openai',
        name: 'GPT-4o',
        description: 'Most capable model for complex reasoning',
        costPerToken: { input: 0.0025, output: 0.01 },
        capabilities: ['reasoning', 'coding', 'analysis'],
        contextWindow: 128000,
        available: true
      },
      {
        id: 'gpt-4o-mini',
        provider: 'openai',
        name: 'GPT-4o Mini',
        description: 'Affordable and intelligent small model',
        costPerToken: { input: 0.00015, output: 0.0006 },
        capabilities: ['reasoning', 'coding', 'analysis'],
        contextWindow: 128000,
        available: true
      }
    ];

    const anthropicModels: AIModel[] = [
      {
        id: 'claude-3-5-sonnet',
        provider: 'anthropic',
        name: 'Claude 3.5 Sonnet',
        description: 'Most intelligent model with strong reasoning',
        costPerToken: { input: 0.003, output: 0.015 },
        capabilities: ['reasoning', 'analysis', 'coding'],
        contextWindow: 200000,
        available: true
      },
      {
        id: 'claude-3-haiku',
        provider: 'anthropic',
        name: 'Claude 3 Haiku',
        description: 'Fastest and most compact model',
        costPerToken: { input: 0.00025, output: 0.00125 },
        capabilities: ['reasoning', 'analysis'],
        contextWindow: 200000,
        available: true
      }
    ];

    const exaModels: AIModel[] = [
      {
        id: 'exa-web-search',
        provider: 'exa',
        name: 'Exa Web Search',
        description: 'Advanced web search and content retrieval',
        costPerToken: { input: 0.001, output: 0.001 },
        capabilities: ['web-search', 'content-retrieval', 'real-time-data'],
        contextWindow: 100000,
        available: true
      }
    ];

    this.activeModels.set('openai', openaiModels);
    this.activeModels.set('anthropic', anthropicModels);
    this.activeModels.set('exa', exaModels);
  }

  private async validateOpenAIKey(apiKey: string): Promise<boolean> {
    try {
      const response = await fetch('https://api.openai.com/v1/models', {
        headers: { 'Authorization': `Bearer ${apiKey}` }
      });
      return response.ok;
    } catch {
      return false;
    }
  }

  private async validateAnthropicKey(apiKey: string): Promise<boolean> {
    try {
      const response = await fetch('https://api.anthropic.com/v1/messages', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json',
          'anthropic-version': '2023-06-01'
        },
        body: JSON.stringify({
          model: 'claude-3-haiku-20240307',
          max_tokens: 1,
          messages: [{ role: 'user', content: 'test' }]
        })
      });
      return response.status !== 401;
    } catch {
      return false;
    }
  }

  private async validateGoogleKey(apiKey: string): Promise<boolean> {
    // Implementation for Google AI validation
    return false; // Placeholder
  }

  private async validateExaKey(apiKey: string): Promise<boolean> {
    try {
      const response = await fetch('https://api.exa.ai/search', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          query: 'test',
          numResults: 1
        })
      });
      return response.status !== 401;
    } catch {
      return false;
    }
  }

  private async getProviderAvailableModels(provider: 'openai' | 'anthropic' | 'google' | 'exa', apiKey: string): Promise<string[]> {
    const providerModels = this.activeModels.get(provider) || [];
    return providerModels.map((model: any) => model.id);
  }

  private async getProviderModels(provider: string, keyData: EncryptedApiKey): Promise<AIModel[]> {
    const providerModels = this.activeModels.get(provider) || [];
    return providerModels.filter((model: any) => keyData.availableModels.includes(model.id));
  }

  private calculateModelScore(model: AIModel, task: string, preferences: ApiPreferences): number {
    let score = 0;
    
    // Preference bonus
    if (model.provider === preferences.preferredProvider) score += 20;
    
    // Task-specific scoring
    if (task.includes('code') && model.capabilities.includes('coding')) score += 15;
    if (task.includes('analyz') && model.capabilities.includes('analysis')) score += 15;
    
    // Cost efficiency (lower cost = higher score)
    const avgCost = (model.costPerToken.input + model.costPerToken.output) / 2;
    score += Math.max(0, 20 - (avgCost * 1000));
    
    return score;
  }

  private async findModel(modelId: string): Promise<AIModel | null> {
    for (const models of this.activeModels.values()) {
      const model = models.find(m => m.id === modelId);
      if (model) return model;
    }
    return null;
  }

  private async checkUsageLimits(memberId: string, userKeys: UserApiKeys): Promise<void> {
    // Check daily limits
    if (userKeys.usage.daily.tokens > 10000) {
      throw new Error('Daily token limit exceeded');
    }
    
    if (userKeys.usage.daily.cost > 100) {
      throw new Error('Daily cost limit exceeded');
    }
  }

  private async startUsageSession(memberId: string, provider: string, modelId: string): Promise<string> {
    const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    // Implementation would start session tracking
    return sessionId;
  }

  private async endUsageSession(memberId: string, sessionId: string, result: any): Promise<void> {
    // Implementation would end session and record results
  }

  private async callProviderAPI(provider: string, modelId: string, prompt: string, apiKey: string): Promise<{ content: string; usage: any }> {
    // Implementation would call the appropriate provider API
    return { content: 'Mock response', usage: { tokens: 100 } };
  }

  private async calculateCost(model: string, tokens: number): Promise<number> {
    const modelData = await this.findModel(model);
    if (!modelData) return 0;
    
    // Simple cost calculation (would be more sophisticated in real implementation)
    return tokens * modelData.costPerToken.input / 1000;
  }

  private async checkUsageAlerts(memberId: string, userKeys: UserApiKeys): Promise<void> {
    if (!userKeys.preferences.costAlerts.enabled) return;
    
    if (userKeys.usage.daily.cost > userKeys.preferences.costAlerts.dailyThreshold) {
      // Send alert (implementation would send Discord DM or notification)
      this.logger.warn(`Daily cost alert for member ${memberId}: $${userKeys.usage.daily.cost}`);
    }
  }

  private async persistUserKeys(memberId: string, userKeys: UserApiKeys): Promise<void> {
    // Implementation would persist to database
  }

  private async loadUserKeys(memberId: string): Promise<UserApiKeys | null> {
    // Implementation would load from database
    return null;
  }
}
