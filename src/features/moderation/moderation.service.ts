import { RedisDatabaseService } from '@/core/database';
import { Injectable, Logger } from '@nestjs/common';
import { EmbedBuilder, GuildMember, PermissionFlagsBits, User } from 'discord.js';
import { Context, IntegerOption, Options, SlashCommand, SlashCommandContext, StringOption, UserOption } from 'necord';

export class KickDto {
  @UserOption({
    name: 'user',
    description: 'The user to kick',
    required: true,
  })
  user!: User;

  @StringOption({
    name: 'reason',
    description: 'Reason for the kick',
    required: false,
  })
  reason?: string;
}

export class BanDto {
  @UserOption({
    name: 'user',
    description: 'The user to ban',
    required: true,
  })
  user!: User;

  @StringOption({
    name: 'reason',
    description: 'Reason for the ban',
    required: false,
  })
  reason?: string;

  @IntegerOption({
    name: 'days',
    description: 'Days of messages to delete (0-7)',
    required: false,
  })
  days?: number;
}

export class TimeoutDto {
  @UserOption({
    name: 'user',
    description: 'The user to timeout',
    required: true,
  })
  user!: User;

  @IntegerOption({
    name: 'duration',
    description: 'Timeout duration in minutes',
    required: true,
  })
  duration!: number;

  @StringOption({
    name: 'reason',
    description: 'Reason for the timeout',
    required: false,
  })
  reason?: string;
}

export class WarnDto {
  @UserOption({
    name: 'user',
    description: 'The user to warn',
    required: true,
  })
  user!: User;

  @StringOption({
    name: 'reason',
    description: 'Reason for the warning',
    required: true,
  })
  reason!: string;
}

export class WarningsDto {
  @UserOption({
    name: 'user',
    description: 'The user to check warnings for',
    required: true,
  })
  user!: User;
}

@Injectable()
export class ModerationService {
  private readonly logger = new Logger(ModerationService.name);

  constructor(
    private readonly redisDatabaseService: RedisDatabaseService,
  ) {}

  @SlashCommand({
    name: 'kick',
    description: 'Kick a member from the server',
    defaultMemberPermissions: [PermissionFlagsBits.KickMembers],
  })
  async onKickCommand(
    @Context() [interaction]: SlashCommandContext,
    @Options() { user, reason }: KickDto
  ) {
    if (!interaction.guild) {
      return interaction.reply({ content: '❌ This command can only be used in a server.', ephemeral: true });
    }

    try {
      const member = await interaction.guild.members.fetch(user.id);
      const executor = interaction.member as GuildMember;

      if (!member.kickable || executor.roles.highest.position <= member.roles.highest.position) {
        await interaction.reply({
          content: `❌ Cannot kick ${user.tag} - insufficient permissions or user has higher role.`,
          ephemeral: true,
        });
        return;
      }

      const kickReason = reason || `Kicked by ${interaction.user.tag}`;
      await member.kick(kickReason);
      this.logger.log(`${user.tag} was kicked from ${interaction.guild.name} by ${interaction.user.tag}. Reason: ${kickReason}`);

      await interaction.reply({
        content: `✅ **${user.tag}** has been kicked from the server.\n${reason ? `**Reason:** ${reason}` : ''}`,
        ephemeral: true,
      });
    } catch (error) {
      this.logger.error(`Failed to kick user ${user.tag}:`, error);
      await interaction.reply({ content: '❌ Failed to kick the user. Please check my permissions and try again.', ephemeral: true });
    }
  }

  @SlashCommand({
    name: 'ban',
    description: 'Ban a member from the server',
    defaultMemberPermissions: [PermissionFlagsBits.BanMembers],
  })
  async onBanCommand(
    @Context() [interaction]: SlashCommandContext,
    @Options() { user, reason, days }: BanDto
  ) {
    if (!interaction.guild) {
      return interaction.reply({ content: '❌ This command can only be used in a server.', ephemeral: true });
    }

    try {
      const executor = interaction.member as GuildMember;
      let member: GuildMember | null = null;

      try {
        member = await interaction.guild.members.fetch(user.id);
      } catch {
        // User might not be in the server, but we can still ban by ID
      }

      if (member && executor.roles.highest.position <= member.roles.highest.position) {
        await interaction.reply({
          content: `❌ Cannot ban ${user.tag} - they have an equal or higher role than you.`,
          ephemeral: true,
        });
        return;
      }

      const banReason = reason || `Banned by ${interaction.user.tag}`;
      const deleteMessageDays = Math.min(days || 0, 7);

      await interaction.guild.members.ban(user.id, {
        reason: banReason,
        deleteMessageSeconds: deleteMessageDays * 24 * 60 * 60,
      });

      this.logger.log(`${user.tag} was banned from ${interaction.guild.name} by ${interaction.user.tag}. Reason: ${banReason}`);

      await interaction.reply({
        content: `✅ **${user.tag}** has been banned from the server.\n${reason ? `**Reason:** ${reason}` : ''}${deleteMessageDays > 0 ? `\n**Messages deleted:** ${deleteMessageDays} days` : ''}`,
        ephemeral: true,
      });
    } catch (error) {
      this.logger.error(`Failed to ban user ${user.tag}:`, error);
      await interaction.reply({ content: '❌ Failed to ban the user. Please check my permissions and try again.', ephemeral: true });
    }
  }

  @SlashCommand({
    name: 'timeout',
    description: 'Timeout a member',
    defaultMemberPermissions: [PermissionFlagsBits.ModerateMembers],
  })
  async onTimeoutCommand(
    @Context() [interaction]: SlashCommandContext,
    @Options() { user, duration, reason }: TimeoutDto
  ) {
    if (!interaction.guild) {
      return interaction.reply({ content: '❌ This command can only be used in a server.', ephemeral: true });
    }

    try {
      const member = await interaction.guild.members.fetch(user.id);
      const executor = interaction.member as GuildMember;

      if (!member.moderatable || executor.roles.highest.position <= member.roles.highest.position) {
        await interaction.reply({
          content: `❌ Cannot timeout ${user.tag} - insufficient permissions or user has higher role.`,
          ephemeral: true,
        });
        return;
      }

      const timeoutDuration = Math.min(duration * 60 * 1000, 28 * 24 * 60 * 60 * 1000);
      const timeoutUntil = new Date(Date.now() + timeoutDuration);
      const timeoutReason = reason || `Timed out by ${interaction.user.tag}`;
      
      await member.timeout(timeoutDuration, timeoutReason);
      this.logger.log(`${user.tag} was timed out in ${interaction.guild.name} by ${interaction.user.tag} for ${duration} minutes. Reason: ${timeoutReason}`);

      const durationText = duration >= 60 
        ? `${Math.floor(duration / 60)} hour(s) ${duration % 60} minute(s)`
        : `${duration} minute(s)`;

      await interaction.reply({
        content: `✅ **${user.tag}** has been timed out for **${durationText}**.\n${reason ? `**Reason:** ${reason}` : ''}\n**Timeout ends:** <t:${Math.floor(timeoutUntil.getTime() / 1000)}:F>`,
        ephemeral: true,
      });
    } catch (error) {
      this.logger.error(`Failed to timeout user ${user.tag}:`, error);
      await interaction.reply({ content: '❌ Failed to timeout the user. Please check my permissions and try again.', ephemeral: true });
    }
  }

  @SlashCommand({
    name: 'warn',
    description: 'Warn a member',
    defaultMemberPermissions: [PermissionFlagsBits.ModerateMembers],
  })
  async onWarnCommand(
    @Context() [interaction]: SlashCommandContext,
    @Options() { user, reason }: WarnDto
  ) {
    if (!interaction.guild) {
      await interaction.reply({
        content: '❌ This command can only be used in a server.',
        ephemeral: true,
      });
      return;
    }

    try {
      const member = await interaction.guild.members.fetch(user.id);
      const executor = interaction.member as GuildMember;

      // Check role hierarchy
      if (executor.roles.highest.position <= member.roles.highest.position) {
        await interaction.reply({
          content: `❌ Cannot warn ${user.tag} - they have an equal or higher role than you.`,
          ephemeral: true,
        });
        return;
      }

      // Store warning in user profile (using preferences field for now)
      let userEntity = await this.redisDatabaseService.findUserByDiscordId(user.id);

      if (!userEntity) {
        userEntity = await this.redisDatabaseService.createUser({
          discordId: user.id,
          username: user.username,
          isActive: true,
          experience: 0,
          balance: 0,
          preferences: { warnings: [] },
        });
      }

      const warning = {
        id: Date.now().toString(),
        reason,
        moderatorId: interaction.user.id,
        guildId: interaction.guild.id,
        timestamp: new Date().toISOString(),
        severity: 'medium' as const,
      };

      const warnings = userEntity.preferences?.warnings || [];
      warnings.push(warning);
      
      const updatedPreferences = {
        ...userEntity.preferences,
        warnings,
      };

      await this.redisDatabaseService.updateUser(userEntity.id, { preferences: updatedPreferences });

      this.logger.log(`${user.tag} was warned in ${interaction.guild.name} by ${interaction.user.tag}. Reason: ${reason}`);

      // Send warning to user via DM
      try {
        const embed = new EmbedBuilder()
          .setColor(0xFF6B35)
          .setTitle('⚠️ Warning Received')
          .setDescription(`You have received a warning in **${interaction.guild.name}**`)
          .addFields([
            { name: 'Reason', value: reason },
            { name: 'Moderator', value: interaction.user.tag },
            { name: 'Date', value: new Date().toLocaleString() },
          ])
          .setFooter({ text: 'Please follow the server rules to avoid further action.' });

        await user.send({ embeds: [embed] });
      } catch {
        // User has DMs disabled, continue anyway
      }

      await interaction.reply({
        content: `⚠️ **${user.tag}** has been warned.\n**Reason:** ${reason}\n**Total warnings:** ${warnings.length}`,
        ephemeral: true,
      });
    } catch (error) {
      this.logger.error(`Failed to warn user ${user.tag}:`, error);
      await interaction.reply({
        content: '❌ Failed to warn the user. Please try again.',
        ephemeral: true,
      });
    }
  }

  @SlashCommand({
    name: 'warnings',
    description: 'View warnings for a user',
    defaultMemberPermissions: [PermissionFlagsBits.ModerateMembers],
  })
  async onWarningsCommand(
    @Context() [interaction]: SlashCommandContext,
    @Options() { user }: WarningsDto
  ) {
    if (!interaction.guild) {
      await interaction.reply({
        content: '❌ This command can only be used in a server.',
        ephemeral: true,
      });
      return;
    }

    try {
      const userEntity = await this.redisDatabaseService.findUserByDiscordId(user.id);

      const warnings = userEntity?.preferences?.warnings || [];
      const guildWarnings = warnings.filter((w: any) => w.guildId === interaction.guild!.id);

      if (guildWarnings.length === 0) {
        await interaction.reply({
          content: `✅ **${user.tag}** has no warnings in this server.`,
          ephemeral: true,
        });
        return;
      }

      const embed = new EmbedBuilder()
        .setColor(0xFF6B35)
        .setTitle(`⚠️ Warnings for ${user.tag}`)
        .setDescription(`Total warnings in this server: **${guildWarnings.length}**`)
        .setThumbnail(user.displayAvatarURL());

      guildWarnings
        .sort((a: any, b: any) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
        .slice(0, 10) // Show last 10 warnings
        .forEach((warning: any, index: number) => {
          embed.addFields([{
            name: `Warning #${guildWarnings.length - index}`,
            value: `**Reason:** ${warning.reason}\n**Moderator:** ${warning.moderator}\n**Date:** ${new Date(warning.timestamp).toLocaleString()}`,
            inline: false,
          }]);
        });

      if (guildWarnings.length > 10) {
        embed.setFooter({ text: `Showing latest 10 of ${guildWarnings.length} warnings` });
      }

      await interaction.reply({ embeds: [embed], ephemeral: true });
    } catch (error) {
      this.logger.error(`Failed to get warnings for user ${user.tag}:`, error);
      await interaction.reply({
        content: '❌ Failed to retrieve warnings. Please try again.',
        ephemeral: true,
      });
    }
  }
}
