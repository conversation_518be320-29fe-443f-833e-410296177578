import { Injectable, Logger } from '@nestjs/common';
import { On, Context } from 'necord';
import { MessageReaction, User, TextChannel } from 'discord.js';
import { TicketSystemService } from '../services/ticket-system.service';

@Injectable()
export class TicketReactionHandler {
  private readonly logger = new Logger(TicketReactionHandler.name);

  constructor(private readonly ticketSystemService: TicketSystemService) {}

  @On('messageReactionAdd')
  async handleTicketReaction(
    @Context() [reaction, user]: [MessageReaction, User]
  ): Promise<void> {
    // Ignore bot reactions
    if (user.bot) return;

    const channel = reaction.message.channel as TextChannel;
    
    // Only handle reactions in ticket channels
    if (!channel.name?.startsWith('ticket-')) return;

    try {
      switch (reaction.emoji.name) {
        case '✅': // Mark as resolved
          await channel.send({
            embeds: [{
              title: '✅ Ticket Marked as Resolved',
              description: `${user} marked this ticket as resolved.\n\nIf your issue is fully resolved, react with 🔒 to close the ticket.`,
              color: 0x4CAF50,
              timestamp: new Date().toISOString()
            }]
          });
          break;

        case '🔒': // Close ticket
          await this.ticketSystemService.closeTicket(channel, user.tag);
          break;

        case '⬆️': // Escalate
          const staffMention = channel.guild?.roles.cache
            .filter((role: any) => ['Admin', 'Manager', 'Lead'].some(title => 
              role.name.toLowerCase().includes(title.toLowerCase())
            ))
            .map((role: any) => `<@&${role.id}>`)
            .join(' ') || 'Staff';

          await channel.send({
            content: staffMention,
            embeds: [{
              title: '⬆️ Ticket Escalated',
              description: `${user} escalated this ticket to senior staff.\n\nThis ticket requires immediate attention from management.`,
              color: 0xFF9800,
              timestamp: new Date().toISOString()
            }]
          });
          break;
      }
    } catch (error) {
      this.logger.error(`Error handling ticket reaction: ${(error as Error).message}`, error);
    }
  }
}
