/**
 * Base Action Handler
 * 
 * Provides common functionality for all panel action handlers
 * Implements shared validation, logging, and error handling
 */

import { Injectable, Logger } from '@nestjs/common';
import { 
  IActionHandler, 
  ActionContext, 
  InteractionResult, 
  PanelRenderData,
  ActionHandlerError
} from '../interfaces/panel-contracts.interface';

@Injectable()
export abstract class BaseActionHandler implements IActionHandler {
  protected readonly logger = new Logger(this.constructor.name);

  abstract readonly handlerId: string;
  abstract readonly supportedPanelTypes: string[];
  abstract readonly supportedActions: string[];

  /**
   * Check if this handler can process the given action
   */
  canHandle(panelType: string, actionId: string): boolean {
    return this.supportedPanelTypes.includes(panelType) && 
           this.supportedActions.includes(actionId);
  }

  /**
   * Main action handling method with error handling and logging
   */
  async handleAction(context: ActionContext): Promise<InteractionResult> {
    const { action, userContext, currentState } = context;

    try {
      this.logger.debug(`Handling action ${action.actionId} for user ${userContext.userId}`);

      // Validate the action
      const isValid = await this.validateAction(context);
      if (!isValid) {
        return this.createErrorResult('You do not have permission to perform this action.');
      }

      // Check cooldown
      if (await this.isOnCooldown(context)) {
        return this.createErrorResult('Please wait before performing this action again.');
      }

      // Record the action attempt
      await this.recordActionAttempt(context);

      // Execute the specific action
      const result = await this.executeAction(context);

      // Record successful action
      if (result.success) {
        await this.recordActionSuccess(context);
        this.logger.debug(`Successfully handled action ${action.actionId}`);
      } else {
        await this.recordActionFailure(context, result.errorMessage);
      }

      return result;

    } catch (error) {
      this.logger.error(`Failed to handle action ${action.actionId}:`, error);
      await this.recordActionError(context, error);
      
      throw new ActionHandlerError(
        `Action handling failed: ${(error as Error).message}`,
        currentState.panelId,
        action.actionId,
        userContext.userId,
        error
      );
    }
  }

  /**
   * Base validation - can be overridden by specific handlers
   */
  async validateAction(context: ActionContext): Promise<boolean> {
    const { action, userContext } = context;

    // Check if action requires specific permission
    if (action.requiresPermission) {
      return userContext.permissions.includes(action.requiresPermission);
    }

    return true;
  }

  /**
   * Abstract method for specific action execution
   * Must be implemented by concrete handlers
   */
  protected abstract executeAction(context: ActionContext): Promise<InteractionResult>;

  /**
   * Check if user is on cooldown for this action
   */
  protected async isOnCooldown(context: ActionContext): Promise<boolean> {
    const { action, userContext, currentState } = context;

    if (!action.cooldownSeconds || action.cooldownSeconds <= 0) {
      return false;
    }

    const lastActionKey = `${action.actionId}_last_used`;
    const lastUsed = currentState.sessionData[lastActionKey] as number;

    if (!lastUsed) {
      return false;
    }

    const cooldownMs = action.cooldownSeconds * 1000;
    const timeSinceLastUse = Date.now() - lastUsed;

    return timeSinceLastUse < cooldownMs;
  }

  /**
   * Record that an action was attempted
   */
  protected async recordActionAttempt(context: ActionContext): Promise<void> {
    // This could integrate with analytics service
    // For now, just log it
    this.logger.debug(`Action attempt: ${context.action.actionId} by ${context.userContext.userId}`);
  }

  /**
   * Record successful action execution
   */
  protected async recordActionSuccess(context: ActionContext): Promise<void> {
    // Update session data with timestamp for cooldown tracking
    const timestampKey = `${context.action.actionId}_last_used`;
    context.currentState.sessionData[timestampKey] = Date.now();
  }

  /**
   * Record failed action execution
   */
  protected async recordActionFailure(context: ActionContext, reason?: string): Promise<void> {
    this.logger.warn(`Action failed: ${context.action.actionId} - ${reason}`);
  }

  /**
   * Record action execution error
   */
  protected async recordActionError(context: ActionContext, error: Error): Promise<void> {
    this.logger.error(`Action error: ${context.action.actionId}`, error);
  }

  /**
   * Create a successful interaction result
   */
  protected createSuccessResult(
    renderData: PanelRenderData,
    stateUpdates?: Record<string, unknown>,
    shouldUpdatePanel: boolean = true
  ): InteractionResult {
    return {
      success: true,
      renderData,
      newState: stateUpdates ? { sessionData: stateUpdates } : undefined,
      shouldUpdatePanel
    };
  }

  /**
   * Create an error interaction result
   */
  protected createErrorResult(errorMessage: string): InteractionResult {
    return {
      success: false,
      errorMessage,
      shouldUpdatePanel: false
    };
  }

  /**
   * Helper method to create simple text response
   */
  protected createTextResponse(content: string, ephemeral: boolean = true): PanelRenderData {
    return {
      embeds: [],
      components: [],
      content,
      ephemeral
    };
  }

  /**
   * Helper method to check if user has required role
   */
  protected hasRequiredRole(userContext: { permissions: string[] }, requiredRole: string): boolean {
    return userContext.permissions.includes(requiredRole) || 
           userContext.permissions.includes('ADMINISTRATOR');
  }

  /**
   * Helper method to sanitize user input
   */
  protected sanitizeInput(input: string, maxLength: number = 200): string {
    if (!input) return '';
    
    return input
      .trim()
      .slice(0, maxLength)
      .replace(/[<@&!>#]/g, ''); // Remove Discord markdown
  }
}
