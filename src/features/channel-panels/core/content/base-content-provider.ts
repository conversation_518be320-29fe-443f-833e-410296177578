/**
 * Base Content Provider
 * 
 * Provides common functionality for all content providers
 * Implements caching, error handling, and data transformation
 */

import { Injectable, Logger } from '@nestjs/common';
import { 
  IContentProvider, 
  ContentRequest, 
  ContentResponse,
  ContentProviderError
} from '../interfaces/panel-contracts.interface';

export interface CacheEntry<T> {
  data: ContentResponse<T>;
  cachedAt: Date;
  expiresAt: Date;
}

@Injectable()
export abstract class BaseContentProvider<T = unknown> implements IContentProvider<T> {
  protected readonly logger = new Logger(this.constructor.name);
  private readonly cache = new Map<string, CacheEntry<T>>();

  abstract readonly providerId: string;
  abstract readonly supportedContentTypes: string[];

  /**
   * Check if this provider can supply the requested content
   */
  canProvide(contentType: string): boolean {
    return this.supportedContentTypes.includes(contentType);
  }

  /**
   * Main content fetching method with caching and error handling
   */
  async getContent(request: ContentRequest): Promise<ContentResponse<T>> {
    const { contentType, parameters, userContext, cacheStrategy, freshnessTolerance } = request;

    try {
      this.logger.debug(`Getting content: ${contentType} for user ${userContext.userId}`);

      // Validate request
      if (!this.canProvide(contentType)) {
        throw new ContentProviderError(
          `Content type not supported: ${contentType}`,
          this.providerId,
          contentType
        );
      }

      // Generate cache key
      const cacheKey = this.generateCacheKey(contentType, parameters, userContext.userId);

      // Check cache if caching is enabled
      if (cacheStrategy !== 'none') {
        const cachedContent = await this.getCachedContent(cacheKey, freshnessTolerance);
        if (cachedContent) {
          this.logger.debug(`Cache hit for ${contentType}`);
          return cachedContent;
        }
      }

      // Fetch fresh content
      this.logger.debug(`Cache miss - fetching fresh content for ${contentType}`);
      const freshContent = await this.fetchContent(request);

      // Cache the result if caching is enabled
      if (cacheStrategy !== 'none') {
        await this.cacheContent(cacheKey, freshContent, cacheStrategy);
      }

      this.logger.debug(`Successfully fetched content: ${contentType}`);
      return freshContent;

    } catch (error) {
      this.logger.error(`Failed to get content ${contentType}:`, error);
      
      if (error instanceof ContentProviderError) {
        throw error;
      }

      throw new ContentProviderError(
        `Content fetching failed: ${(error as Error).message}`,
        this.providerId,
        contentType,
        error
      );
    }
  }

  /**
   * Check if cached content is still valid
   */
  isContentFresh(content: ContentResponse<T>, tolerance: number): boolean {
    const now = new Date();
    const contentAge = (now.getTime() - content.timestamp.getTime()) / 1000; // seconds
    
    return contentAge <= tolerance;
  }

  /**
   * Abstract method for fetching content - must be implemented by concrete providers
   */
  protected abstract fetchContent(request: ContentRequest): Promise<ContentResponse<T>>;

  /**
   * Generate a unique cache key for the request
   */
  protected generateCacheKey(
    contentType: string, 
    parameters: Record<string, unknown>, 
    userId: string
  ): string {
    const paramString = JSON.stringify(parameters);
    const hash = this.simpleHash(paramString);
    return `${this.providerId}:${contentType}:${userId}:${hash}`;
  }

  /**
   * Get cached content if available and fresh
   */
  protected async getCachedContent(
    cacheKey: string, 
    freshnessTolerance: number
  ): Promise<ContentResponse<T> | null> {
    const cached = this.cache.get(cacheKey);
    
    if (!cached) {
      return null;
    }

    const now = new Date();
    
    // Check if cache entry has expired
    if (now > cached.expiresAt) {
      this.cache.delete(cacheKey);
      return null;
    }

    // Check if content is fresh enough
    if (!this.isContentFresh(cached.data, freshnessTolerance)) {
      return null;
    }

    return cached.data;
  }

  /**
   * Cache content with appropriate expiration
   */
  protected async cacheContent(
    cacheKey: string, 
    content: ContentResponse<T>, 
    strategy: 'memory' | 'persistent' | 'hybrid'
  ): Promise<void> {
    const now = new Date();
    const expiresAt = content.expiresAt || new Date(now.getTime() + (15 * 60 * 1000)); // 15 minutes default

    const cacheEntry: CacheEntry<T> = {
      data: content,
      cachedAt: now,
      expiresAt
    };

    // For now, only implement memory caching
    // In production, 'persistent' and 'hybrid' would use Redis or database
    if (strategy === 'memory' || strategy === 'hybrid') {
      this.cache.set(cacheKey, cacheEntry);
    }

    // TODO: Implement persistent caching for 'persistent' and 'hybrid' strategies
    if (strategy === 'persistent' || strategy === 'hybrid') {
      await this.persistContent(cacheKey, cacheEntry);
    }
  }

  /**
   * Persist content to external storage (Redis, database, etc.)
   * Override in subclasses if needed
   */
  protected async persistContent(cacheKey: string, cacheEntry: CacheEntry<T>): Promise<void> {
    // Default implementation does nothing
    // Override in subclasses to implement actual persistence
    this.logger.debug(`Persistent caching not implemented for ${this.providerId}`);
  }

  /**
   * Create a successful content response
   */
  protected createResponse(
    data: T, 
    source: string = this.providerId,
    expiresIn?: number // seconds
  ): ContentResponse<T> {
    const now = new Date();
    return {
      data,
      source,
      timestamp: now,
      expiresAt: expiresIn ? new Date(now.getTime() + (expiresIn * 1000)) : undefined,
      metadata: {
        providerId: this.providerId,
        fetchedAt: now.toISOString()
      }
    };
  }

  /**
   * Transform data using provided transformation config
   */
  protected transformData<U>(
    data: U, 
    transformation?: {
      template?: string;
      filters?: Array<{
        field: string;
        operator: 'equals' | 'contains' | 'greater' | 'less';
        value: any;
      }>;
      mapping?: Record<string, string>;
    }
  ): U {
    if (!transformation) {
      return data;
    }

    let result = data;

    // Apply filters
    if (transformation.filters && Array.isArray(result)) {
      result = (result as any[]).filter((item: any) => {
        return transformation.filters!.every(filter => {
          const fieldValue = this.getNestedValue(item, filter.field);
          return this.applyFilter(fieldValue, filter.operator, filter.value);
        });
      }) as U;
    }

    // Apply field mapping
    if (transformation.mapping && typeof result === 'object' && result !== null) {
      result = this.applyFieldMapping(result, transformation.mapping);
    }

    return result;
  }

  /**
   * Apply a single filter condition
   */
  private applyFilter(fieldValue: any, operator: string, filterValue: any): boolean {
    switch (operator) {
      case 'equals':
        return fieldValue === filterValue;
      case 'contains':
        return String(fieldValue).toLowerCase().includes(String(filterValue).toLowerCase());
      case 'greater':
        return Number(fieldValue) > Number(filterValue);
      case 'less':
        return Number(fieldValue) < Number(filterValue);
      default:
        return true;
    }
  }

  /**
   * Apply field mapping to transform object structure
   */
  private applyFieldMapping<U>(data: U, mapping: Record<string, string>): U {
    if (Array.isArray(data)) {
      return data.map((item: any) => this.applyFieldMapping(item, mapping)) as U;
    }

    if (typeof data === 'object' && data !== null) {
      const result = { ...data };
      
      Object.entries(mapping).forEach(([oldField, newField]) => {
        if (oldField in result) {
          (result as any)[newField] = (result as any)[oldField];
          delete (result as any)[oldField];
        }
      });

      return result;
    }

    return data;
  }

  /**
   * Get nested value from object using dot notation
   */
  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined;
    }, obj);
  }

  /**
   * Simple hash function for cache keys
   */
  private simpleHash(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36);
  }

  /**
   * Clean up expired cache entries
   */
  protected cleanupExpiredCache(): void {
    const now = new Date();
    const expiredKeys: string[] = [];

    this.cache.forEach((entry, key) => {
      if (now > entry.expiresAt) {
        expiredKeys.push(key);
      }
    });

    expiredKeys.forEach(key => {
      this.cache.delete(key);
    });

    if (expiredKeys.length > 0) {
      this.logger.debug(`Cleaned up ${expiredKeys.length} expired cache entries`);
    }
  }

  /**
   * Get cache statistics for monitoring
   */
  getCacheStats(): {
    totalEntries: number;
    expiredEntries: number;
    cacheHitRate: number;
    oldestEntry?: Date;
  } {
    const now = new Date();
    let expiredCount = 0;
    let oldestEntry: Date | undefined;

    this.cache.forEach(entry => {
      if (now > entry.expiresAt) {
        expiredCount++;
      }
      
      if (!oldestEntry || entry.cachedAt < oldestEntry) {
        oldestEntry = entry.cachedAt;
      }
    });

    return {
      totalEntries: this.cache.size,
      expiredEntries: expiredCount,
      cacheHitRate: 0, // Would track this with actual hit/miss counters
      oldestEntry
    };
  }
}
