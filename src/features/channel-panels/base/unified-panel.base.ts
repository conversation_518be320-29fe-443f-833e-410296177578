import { Injectable, Logger } from '@nestjs/common';
import { BaseChannelPanel, ChannelContext, PanelConfig, PanelContent, PanelUpdateStrategy } from '../interfaces/panel.interface';
import { ConsolidatedPanelCoreService } from '../services/consolidated-panel-core.service';
import { UserManagementService } from '../services/user-management.service';
import { DynamicContentService } from '../services/dynamic-content.service';

/**
 * Unified base class that eliminates duplicate shouldActivate logic
 * All panels should extend this instead of BaseChannelPanel directly
 */
@Injectable()
export abstract class UnifiedPanelBase extends BaseChannelPanel {
  protected readonly logger = new Logger(this.constructor.name);
  
  constructor(
    protected readonly panelCoreService: ConsolidatedPanelCoreService,
    protected readonly userManagementService: UserManagementService,
    protected readonly dynamicContentService: DynamicContentService
  ) {
    super();
  }

  // Access services through consolidated core service
  protected get panelActivationService() {
    return this.panelCoreService;
  }

  protected get analyticsService() {
    return this.panelCoreService;
  }

  protected get userStateService() {
    return this.userManagementService;
  }

  /**
   * Centralized activation check - no need to override in child classes
   * Uses the PanelActivationService to eliminate duplicate logic
   */
  shouldActivate(context: ChannelContext): boolean {
    const shouldActivate = this.panelActivationService.shouldActivatePanel(this.config, context);
    
    // Track activation attempts for analytics
    if (shouldActivate) {
      this.analyticsService.trackEvent({
        id: `activation_${this.config.id}_${Date.now()}`,
        panelId: this.config.id,
        channelId: context.channelId,
        eventType: 'interaction',
        timestamp: new Date(),
        data: {
          action: 'panel_activated',
          channelName: context.channelName,
          categoryName: context.categoryName,
        },
      }).catch(error => {
        this.logger.error(`Failed to track activation: ${(error as Error).message}`);
      });
    }
    
    return shouldActivate;
  }

  /**
   * Get activation reasons for debugging
   */
  getActivationReasons(context: ChannelContext): string[] {
    return this.panelActivationService.getActivationReasons(this.config, context);
  }

  /**
   * Validate this panel's configuration
   */
  validateConfig(): string[] {
    return this.panelActivationService.validatePanelConfig(this.config);
  }

  /**
   * Default update strategy - panels can override for custom behavior
   */
  getUpdateStrategy(): PanelUpdateStrategy {
    return {
      type: 'interval',
      interval: this.getUpdateInterval(),
      conditions: []
    };
  }

  /**
   * Enhanced update check with strategy support and dynamic content integration
   */
  needsUpdate(lastUpdate: Date): boolean {
    const strategy = this.getUpdateStrategy();
    
    switch (strategy.type) {
      case 'interval':
        return super.needsUpdate(lastUpdate);
      
      case 'event-driven':
        // Event-driven panels update based on external events
        // This would be handled by the orchestrator
        return false;
      
      case 'conditional':
        // Check custom conditions including dynamic content freshness
        return this.evaluateUpdateConditions(strategy.conditions || [], lastUpdate);
      
      case 'manual':
        // Manual updates only
        return false;
      
      default:
        return super.needsUpdate(lastUpdate);
    }
  }

  /**
   * Evaluate custom update conditions
   */
  private evaluateUpdateConditions(conditions: any[], lastUpdate: Date): boolean {
    if (conditions.length === 0) return false;
    
    // Default implementation - panels can override for custom logic
    const now = new Date();
    const timeSinceUpdate = now.getTime() - lastUpdate.getTime();
    
    return conditions.some(condition => {
      switch (condition.type) {
        case 'time-window':
          return condition.timeWindow && timeSinceUpdate >= condition.timeWindow;
        case 'user-activity':
          // Would need integration with activity tracking
          return false;
        case 'data-change':
          // Would need integration with data change detection
          return false;
        default:
          return false;
      }
    });
  }

  /**
   * Get enhanced panel health metrics with performance data
   */
  getHealthMetrics(): {
    configValid: boolean;
    configIssues: string[];
    lastUpdate?: Date;
    updateInterval: number;
    updateStrategy: PanelUpdateStrategy;
    performance?: {
      averageRenderTime: number;
      errorRate: number;
      cacheHitRate: number;
    };
  } {
    const issues = this.validateConfig();
    
    return {
      configValid: issues.length === 0,
      configIssues: issues,
      updateInterval: this.getUpdateInterval(),
      updateStrategy: this.getUpdateStrategy()
    };
  }

  /**
   * Generate personalized panel content with analytics tracking
   */
  async generatePersonalizedPanel(
    context: ChannelContext,
    userId?: string,
    sessionId?: string
  ): Promise<PanelContent> {
    const startTime = Date.now();
    
    try {
      // Track panel view
      if (userId) {
        await this.analyticsService.trackPanelView(userId, this.config.id, context, sessionId);
      }

      // Get user preferences for personalization
      let personalizedConfig = {};
      if (userId) {
        personalizedConfig = await this.userStateService.getPersonalizedPanelConfig(
          userId,
          this.config.id,
          context
        );
      }

      // Get dynamic content
      const dynamicContent = await this.dynamicContentService.getContentForPanel(this.config.id);

      // Generate base panel content
      const baseContent = await this.generatePanel(context);
      
      // Enhance with dynamic content and personalization
      const enhancedContent = await this.enhanceWithDynamicContent(baseContent, dynamicContent, personalizedConfig);
      
      // Track performance
      const renderTime = Date.now() - startTime;
      await this.analyticsService.trackPerformance(this.config.id, context, {
        responseTime: renderTime,
        renderTime,
        dataLoadTime: 0, // Would track actual data load time
        memoryUsage: process.memoryUsage().heapUsed,
      });

      return enhancedContent;
    } catch (error) {
      // Track error
      await this.analyticsService.trackError(
        userId,
        this.config.id,
        context,
        {
          errorCode: 'PANEL_GENERATION_ERROR',
          errorMessage: (error as Error).message,
          stackTrace: (error as Error).stack,
        },
        sessionId
      );
      
      throw error;
    }
  }

  /**
   * Enhanced interaction handling with analytics and state tracking
   */
  async handleEnhancedInteraction(
    interaction: any,
    context: ChannelContext,
    userId?: string,
    sessionId?: string
  ): Promise<void> {
    const startTime = Date.now();
    
    try {
      // Track interaction start
      if (userId) {
        await this.analyticsService.trackInteraction(
          userId,
          this.config.id,
          context,
          {
            componentId: interaction.customId || 'unknown',
            componentType: this.getComponentType(interaction),
            action: interaction.customId || 'unknown',
            duration: 0, // Will be updated after completion
          },
          sessionId
        );
      }

      // Handle the actual interaction
      await this.handleInteraction(interaction, context);
      
      // Track successful interaction
      const duration = Date.now() - startTime;
      if (userId) {
        await this.analyticsService.trackInteraction(
          userId,
          this.config.id,
          context,
          {
            componentId: interaction.customId || 'unknown',
            componentType: this.getComponentType(interaction),
            action: interaction.customId || 'unknown',
            duration,
            result: 'success',
          },
          sessionId
        );

        // Update user interaction history
        await this.userStateService.trackInteraction(
          userId,
          this.config.id,
          context.channelId,
          context.guildId,
          {
            action: interaction.customId || 'unknown',
            componentId: interaction.customId || 'unknown',
            context: {
              channelName: context.channelName,
              categoryName: context.categoryName,
            },
            duration,
            result: 'success',
          }
        );
      }
    } catch (error) {
      // Track error
      if (userId) {
        await this.analyticsService.trackError(
          userId,
          this.config.id,
          context,
          {
            errorCode: 'INTERACTION_ERROR',
            errorMessage: (error as Error).message,
            stackTrace: (error as Error).stack,
            componentId: interaction.customId,
            action: interaction.customId,
          },
          sessionId
        );
      }
      
      throw error;
    }
  }

  /**
   * Get user-specific panel preferences
   */
  async getUserPreferences(userId: string, context: ChannelContext): Promise<any> {
    return this.userStateService.getPersonalizedPanelConfig(
      userId,
      this.config.id,
      context
    );
  }

  /**
   * Update user preferences
   */
  async updateUserPreferences(
    userId: string,
    context: ChannelContext,
    preferences: any
  ): Promise<boolean> {
    return this.userStateService.updateUserPreferences(
      userId,
      this.config.id,
      context.channelId,
      context.guildId,
      preferences
    );
  }

  /**
   * Get panel analytics insights
   */
  async getAnalyticsInsights(
    timeRange: { start: Date; end: Date },
    guildId?: string
  ): Promise<any> {
    return this.analyticsService.getPanelInsights(
      this.config.id,
      timeRange,
      guildId
    );
  }

  /**
   * Abstract method for enhancing content with dynamic data
   */
  protected async enhanceWithDynamicContent(
    baseContent: PanelContent,
    dynamicContent: any[],
    personalizedConfig: any
  ): Promise<PanelContent> {
    // Default implementation - panels can override for custom enhancement logic
    return baseContent;
  }

  /**
   * Helper method to determine component type from interaction
   */
  private getComponentType(interaction: any): 'button' | 'select' | 'modal' | 'embed' | 'form' {
    if (interaction.isButton?.()) return 'button';
    if (interaction.isStringSelectMenu?.()) return 'select';
    if (interaction.isModalSubmit?.()) return 'modal';
    return 'button'; // Default fallback
  }
}
