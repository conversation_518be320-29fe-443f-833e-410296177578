import { Injectable, Logger } from '@nestjs/common';
import { Guild, TextChannel, PermissionFlagsBits, ChannelType, ButtonInteraction } from 'discord.js';

@Injectable()
export class TicketSystemService {
  private readonly logger = new Logger(TicketSystemService.name);
  private ticketCounter = 1000;

  async createSupportTicket(
    interaction: ButtonInteraction, 
    category: string, 
    priority: 'low' | 'medium' | 'high' | 'critical' = 'medium'
  ): Promise<void> {
    const guild = interaction.guild;
    const user = interaction.user;
    
    if (!guild) {
      await interaction.reply({ content: '❌ This command can only be used in a server.', ephemeral: true });
      return;
    }

    try {
      // Generate ticket number
      const ticketNumber = this.ticketCounter++;
      const channelName = `ticket-${category}-${ticketNumber}`;

      // Find or create ticket category
      let ticketCategory = guild.channels.cache.find(
        c => c.type === ChannelType.GuildCategory && c.name === '🎫 Support Tickets'
      );

      if (!ticketCategory) {
        ticketCategory = await guild.channels.create({
          name: '🎫 Support Tickets',
          type: ChannelType.GuildCategory,
        });
      }

      // Find staff roles (adjust these role names to match your server)
      const staffRoles = guild.roles.cache.filter((role: any) => 
        ['Support', 'Staff', 'Admin', 'Moderator', 'Helper'].some(staffName => 
          role.name.toLowerCase().includes(staffName.toLowerCase())
        )
      );

      // Create ticket channel
      const ticketChannel = await guild.channels.create({
        name: channelName,
        type: ChannelType.GuildText,
        parent: ticketCategory.id,
        permissionOverwrites: [
          {
            id: guild.id, // @everyone
            deny: [PermissionFlagsBits.ViewChannel],
          },
          {
            id: user.id, // Ticket creator
            allow: [
              PermissionFlagsBits.ViewChannel,
              PermissionFlagsBits.SendMessages,
              PermissionFlagsBits.ReadMessageHistory,
            ],
          },
          // Add staff permissions
          ...staffRoles.map((role: any) => ({
            id: role.id,
            allow: [
              PermissionFlagsBits.ViewChannel,
              PermissionFlagsBits.SendMessages,
              PermissionFlagsBits.ReadMessageHistory,
              PermissionFlagsBits.ManageMessages,
            ],
          })),
        ],
      });

      // Send ticket info to channel
      await ticketChannel.send({
        content: `${user} ${staffRoles.map((r: any) => `<@&${r.id}>`).join(' ')}`,
        embeds: [{
          title: `🎫 Support Ticket #${ticketNumber}`,
          description: `**Category:** ${category.charAt(0).toUpperCase() + category.slice(1)}\n**Priority:** ${priority.toUpperCase()}\n**Created by:** ${user.tag}`,
          color: priority === 'critical' ? 0xFF0000 : priority === 'high' ? 0xFF9800 : priority === 'medium' ? 0x2196F3 : 0x4CAF50,
          timestamp: new Date(),
          footer: { text: 'React with ✅ when resolved or 🔒 to close ticket' }
        }]
      });

      // Add reactions for ticket management
      const message = await ticketChannel.send('**Ticket Controls:**');
      await message.react('✅'); // Mark as resolved
      await message.react('🔒'); // Close ticket
      await message.react('⬆️'); // Escalate

      await interaction.reply({
        content: `✅ **Ticket Created Successfully!**\n\nYour support ticket has been created: ${ticketChannel}\n\n**Ticket #${ticketNumber}**\n**Category:** ${category}\n**Priority:** ${priority}\n\nOur support team will assist you shortly!`,
        ephemeral: true
      });

      this.logger.log(`Ticket #${ticketNumber} created by ${user.tag} in category ${category}`);

    } catch (error) {
      this.logger.error(`Failed to create ticket: ${(error as Error).message}`, error);
      await interaction.reply({
        content: '❌ Failed to create support ticket. Please try again or contact an administrator.',
        ephemeral: true
      });
    }
  }

  async closeTicket(channel: TextChannel, closedBy: string): Promise<void> {
    try {
      if (!channel.name.startsWith('ticket-')) return;

      // Send closing message
      await channel.send({
        embeds: [{
          title: '🔒 Ticket Closed',
          description: `This ticket has been closed by ${closedBy}.`,
          color: 0x607D8B,
          timestamp: new Date()
        }]
      });

      // Archive the channel (move to archive category or delete after delay)
      setTimeout(async () => {
        try {
          let archiveCategory = channel.guild.channels.cache.find(
            c => c.type === ChannelType.GuildCategory && c.name === '🗄️ Ticket Archive'
          );

          if (!archiveCategory) {
            archiveCategory = await channel.guild.channels.create({
              name: '🗄️ Ticket Archive',
              type: ChannelType.GuildCategory,
            });
          }

          await channel.setParent(archiveCategory.id);
          await channel.edit({ name: `closed-${channel.name}` });
          
          this.logger.log(`Ticket ${channel.name} archived`);
        } catch (error) {
          this.logger.error(`Failed to archive ticket: ${(error as Error).message}`);
        }
      }, 5000); // 5 second delay before archiving

    } catch (error) {
      this.logger.error(`Failed to close ticket: ${(error as Error).message}`, error);
    }
  }
}
