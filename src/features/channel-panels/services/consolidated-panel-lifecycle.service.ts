import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { ConfigService } from '@nestjs/config';
import { DatabaseService } from '@/core/database';
import { ConsolidatedPanelCoreService } from './consolidated-panel-core.service';
import { PanelConfig, ChannelContext } from '../interfaces/panel.interface';

/**
 * Consolidated Panel Lifecycle Service
 * Replaces 7 services:
 * - PanelDeploymentOrchestratorService
 * - PanelRecoveryService  
 * - PanelVersioningService
 * - PanelCleanupService
 * - PanelCleanupManagerService
 * - PanelCleanupIntegrationService
 * - AutoCleanupService
 * 
 * Handles complete panel lifecycle: deployment, versioning, recovery, and cleanup.
 */
@Injectable()
export class ConsolidatedPanelLifecycleService {
  private readonly logger = new Logger(ConsolidatedPanelLifecycleService.name);
  private readonly deploymentQueue = new Map<string, DeploymentTask>();
  private readonly versionHistory = new Map<string, PanelVersion[]>();
  private readonly cleanupTasks = new Map<string, CleanupTask>();
  
  constructor(
    private readonly configService: ConfigService,
    private readonly databaseService: DatabaseService,
    private readonly panelCoreService: ConsolidatedPanelCoreService,
  ) {
    this.initializeLifecycleService();
  }

  // === DEPLOYMENT ORCHESTRATION (replaces PanelDeploymentOrchestratorService) ===

  /**
   * Orchestrates panel deployment across multiple channels
   */
  async deployPanel(config: PanelConfig, targets: ChannelContext[]): Promise<DeploymentResult> {
    try {
      this.logger.log(`Starting deployment of panel ${config.id} to ${targets.length} channels`);

      const deploymentId = this.generateDeploymentId(config.id);
      const deploymentTask: DeploymentTask = {
        id: deploymentId,
        panelId: config.id,
        config,
        targets,
        status: 'pending',
        startedAt: new Date(),
        results: [],
        errors: []
      };

      this.deploymentQueue.set(deploymentId, deploymentTask);

      // Execute deployment
      const results = await this.executeDeployment(deploymentTask);
      
      // Update deployment status
      deploymentTask.status = results.success ? 'completed' : 'failed';
      deploymentTask.completedAt = new Date();
      deploymentTask.results = results.channelResults;
      deploymentTask.errors = results.errors;

      // Store deployment record
      await this.storeDeploymentRecord(deploymentTask);

      // Create version record
      await this.createVersionRecord(config, deploymentId, results.success);

      this.logger.log(`Deployment ${deploymentId} ${deploymentTask.status}: ${results.success ? results.channelResults.length : 0}/${targets.length} channels`);

      return {
        deploymentId,
        success: results.success,
        channelResults: results.channelResults,
        errors: results.errors,
        summary: this.generateDeploymentSummary(deploymentTask)
      };

    } catch (error) {
      this.logger.error('Panel deployment failed:', error);
      throw error;
    }
  }

  /**
   * Rolls back a panel deployment
   */
  async rollbackDeployment(deploymentId: string): Promise<RollbackResult> {
    try {
      const deployment = await this.getDeploymentRecord(deploymentId);
      if (!deployment) {
        throw new Error(`Deployment ${deploymentId} not found`);
      }

      this.logger.log(`Rolling back deployment ${deploymentId}`);

      const rollbackResults: ChannelRollbackResult[] = [];
      const errors: string[] = [];

      for (const target of deployment.targets) {
        try {
          await this.panelCoreService.deactivatePanel(deployment.panelId);
          
          // Restore previous version if available
          const previousVersion = await this.getPreviousVersion(deployment.panelId, target.channelId);
          if (previousVersion) {
            await this.panelCoreService.activatePanel(previousVersion.config, target);
          }

          rollbackResults.push({
            channelId: target.channelId,
            success: true,
            restoredVersion: previousVersion?.version || null
          });

        } catch (error) {
          errors.push(`Failed to rollback in channel ${target.channelId}: ${(error as Error).message}`);
          rollbackResults.push({
            channelId: target.channelId,
            success: false,
            error: (error as Error).message
          });
        }
      }

      const success = errors.length === 0;
      
      // Record rollback
      await this.recordRollback(deploymentId, success, rollbackResults, errors);

      return {
        deploymentId,
        success,
        channelResults: rollbackResults,
        errors
      };

    } catch (error) {
      this.logger.error(`Rollback failed for deployment ${deploymentId}:`, error);
      throw error;
    }
  }

  // === VERSIONING (replaces PanelVersioningService) ===

  /**
   * Creates a new version of a panel configuration
   */
  async createVersion(panelId: string, config: PanelConfig, changeNotes?: string): Promise<PanelVersion> {
    try {
      const currentVersions = this.versionHistory.get(panelId) || [];
      const newVersionNumber = this.calculateNextVersion(currentVersions);

      const version: PanelVersion = {
        id: `${panelId}_v${newVersionNumber}`,
        panelId,
        version: newVersionNumber,
        config,
        changeNotes: changeNotes || 'Automated version creation',
        createdAt: new Date(),
        createdBy: 'system',
        isActive: true,
        deploymentId: null
      };

      // Mark previous versions as inactive
      currentVersions.forEach(v => v.isActive = false);
      
      // Add new version
      currentVersions.push(version);
      this.versionHistory.set(panelId, currentVersions);

      // Persist to database
      await this.storeVersionRecord(version);

      this.logger.log(`Created version ${newVersionNumber} for panel ${panelId}`);
      return version;

    } catch (error) {
      this.logger.error(`Failed to create version for panel ${panelId}:`, error);
      throw error;
    }
  }

  /**
   * Gets version history for a panel
   */
  async getVersionHistory(panelId: string): Promise<PanelVersion[]> {
    try {
      // Check cache first
      if (this.versionHistory.has(panelId)) {
        return this.versionHistory.get(panelId)!;
      }

      // Load from database
      const result = await this.databaseService.query(
        'SELECT * FROM panel_versions WHERE panel_id = $1 ORDER BY version DESC',
        [panelId]
      );

      const versions = result.map((row: any) => ({
        id: row.id,
        panelId: row.panel_id,
        version: row.version,
        config: JSON.parse(row.config),
        changeNotes: row.change_notes,
        createdAt: row.created_at,
        createdBy: row.created_by,
        isActive: row.is_active,
        deploymentId: row.deployment_id
      }));

      this.versionHistory.set(panelId, versions);
      return versions;

    } catch (error) {
      this.logger.error(`Failed to get version history for panel ${panelId}:`, error);
      return [];
    }
  }

  // === RECOVERY (replaces PanelRecoveryService) ===

  /**
   * Recovers a failed panel deployment
   */
  async recoverPanel(panelId: string, strategy: RecoveryStrategy = 'restart'): Promise<RecoveryResult> {
    try {
      this.logger.log(`Starting recovery for panel ${panelId} using strategy: ${strategy}`);

      const panelInfo = await this.panelCoreService.getPanelInfo(panelId);
      if (!panelInfo.state) {
        throw new Error(`Panel ${panelId} not found`);
      }

      let recoveryResult: RecoveryResult;

      switch (strategy) {
        case 'restart':
          recoveryResult = await this.restartRecovery(panelId, panelInfo);
          break;
        
        case 'rollback':
          recoveryResult = await this.rollbackRecovery(panelId);
          break;
        
        case 'redeploy':
          recoveryResult = await this.redeployRecovery(panelId, panelInfo);
          break;
        
        default:
          throw new Error(`Unknown recovery strategy: ${strategy}`);
      }

      // Record recovery attempt
      await this.recordRecoveryAttempt(panelId, strategy, recoveryResult);

      return recoveryResult;

    } catch (error) {
      this.logger.error(`Panel recovery failed for ${panelId}:`, error);
      throw error;
    }
  }

  /**
   * Checks panel health and triggers recovery if needed
   */
  async performHealthCheck(panelId: string): Promise<HealthCheckResult> {
    try {
      const panelInfo = await this.panelCoreService.getPanelInfo(panelId);
      
      const healthCheck: HealthCheckResult = {
        panelId,
        isHealthy: true,
        issues: [],
        recommendations: [],
        lastChecked: new Date()
      };

      // Check if panel is active
      if (!panelInfo.isActive) {
        healthCheck.isHealthy = false;
        healthCheck.issues.push('Panel is not active');
        healthCheck.recommendations.push('Consider reactivating the panel');
      }

      // Check recent errors
      const recentErrors = await this.getRecentErrors(panelId);
      if (recentErrors.length > 5) {
        healthCheck.isHealthy = false;
        healthCheck.issues.push(`High error rate: ${recentErrors.length} errors in last hour`);
        healthCheck.recommendations.push('Review error logs and consider restarting panel');
      }

      // Check performance metrics
      const analytics = panelInfo.analytics;
      if (analytics && analytics.lastActivity) {
        const hoursSinceActivity = (Date.now() - analytics.lastActivity.getTime()) / (1000 * 60 * 60);
        if (hoursSinceActivity > 24) {
          healthCheck.issues.push('No activity in 24+ hours');
          healthCheck.recommendations.push('Check if panel is still needed or requires attention');
        }
      }

      return healthCheck;

    } catch (error) {
      this.logger.error(`Health check failed for panel ${panelId}:`, error);
      return {
        panelId,
        isHealthy: false,
        issues: [`Health check failed: ${(error as Error).message}`],
        recommendations: ['Manual investigation required'],
        lastChecked: new Date()
      };
    }
  }

  // === CLEANUP (replaces multiple cleanup services) ===

  /**
   * Schedules automatic cleanup of inactive panels
   */
  @Cron(CronExpression.EVERY_DAY_AT_2AM)
  async performScheduledCleanup(): Promise<void> {
    this.logger.log('Starting scheduled panel cleanup...');

    try {
      const cleanupTasks = await this.identifyCleanupCandidates();
      
      for (const task of cleanupTasks) {
        await this.executeCleanupTask(task);
      }

      this.logger.log(`Scheduled cleanup completed: ${cleanupTasks.length} tasks processed`);
    } catch (error) {
      this.logger.error('Scheduled cleanup failed:', error);
    }
  }

  /**
   * Performs immediate cleanup of specified panels
   */
  async cleanupPanels(panelIds: string[], cleanupType: CleanupType = 'soft'): Promise<CleanupResult[]> {
    const results: CleanupResult[] = [];

    for (const panelId of panelIds) {
      try {
        const task: CleanupTask = {
          id: `cleanup_${panelId}_${Date.now()}`,
          panelId,
          type: cleanupType,
          status: 'pending',
          createdAt: new Date()
        };

        const result = await this.executeCleanupTask(task);
        results.push(result);

      } catch (error) {
        results.push({
          panelId,
          success: false,
          error: (error as Error).message,
          cleanedItems: []
        });
      }
    }

    return results;
  }

  // === PRIVATE METHODS ===

  private async initializeLifecycleService(): Promise<void> {
    this.logger.log('Initializing panel lifecycle service...');
    
    // Load active deployments
    await this.loadActiveDeployments();
    
    // Load version histories
    await this.loadVersionHistories();
    
    this.logger.log('Panel lifecycle service initialized');
  }

  private async executeDeployment(task: DeploymentTask): Promise<{
    success: boolean;
    channelResults: ChannelDeploymentResult[];
    errors: string[];
  }> {
    const channelResults: ChannelDeploymentResult[] = [];
    const errors: string[] = [];

    for (const target of task.targets) {
      try {
        const success = await this.panelCoreService.activatePanel(task.config, target);
        
        channelResults.push({
          channelId: target.channelId,
          success,
          deployedAt: new Date()
        });

      } catch (error) {
        errors.push(`Failed to deploy to channel ${target.channelId}: ${(error as Error).message}`);
        channelResults.push({
          channelId: target.channelId,
          success: false,
          error: (error as Error).message
        });
      }
    }

    return {
      success: errors.length === 0,
      channelResults,
      errors
    };
  }

  private async createVersionRecord(config: PanelConfig, deploymentId: string, success: boolean): Promise<void> {
    if (success) {
      await this.createVersion(config.id, config, `Deployment ${deploymentId}`);
    }
  }

  private async restartRecovery(panelId: string, panelInfo: any): Promise<RecoveryResult> {
    // Deactivate and reactivate panel
    await this.panelCoreService.deactivatePanel(panelId);
    
    // Wait a moment before reactivation
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Get the latest configuration
    const versions = await this.getVersionHistory(panelId);
    const latestVersion = versions.find(v => v.isActive);
    
    if (!latestVersion) {
      throw new Error('No active version found for restart');
    }

    // Create a dummy context for reactivation
    const context: ChannelContext = { 
      channelId: 'recovery', 
      channelName: 'recovery', 
      guildId: 'system', 
      guildName: 'system',
      userId: 'system' 
    };
    const success = await this.panelCoreService.activatePanel(latestVersion.config, context);

    return {
      panelId,
      strategy: 'restart',
      success,
      message: success ? 'Panel successfully restarted' : 'Panel restart failed',
      recoveredAt: new Date()
    };
  }

  private async rollbackRecovery(panelId: string): Promise<RecoveryResult> {
    const versions = await this.getVersionHistory(panelId);
    const previousVersion = versions.find((v, index) => index === 1); // Second most recent

    if (!previousVersion) {
      throw new Error('No previous version available for rollback');
    }

    // Activate previous version
    const context: ChannelContext = { 
      channelId: 'recovery', 
      channelName: 'recovery', 
      guildId: 'system', 
      guildName: 'system',
      userId: 'system' 
    };
    const success = await this.panelCoreService.activatePanel(previousVersion.config, context);

    return {
      panelId,
      strategy: 'rollback',
      success,
      message: success ? `Rolled back to version ${previousVersion.version}` : 'Rollback failed',
      recoveredAt: new Date()
    };
  }

  private async redeployRecovery(panelId: string, panelInfo: any): Promise<RecoveryResult> {
    // Force a complete redeployment
    const versions = await this.getVersionHistory(panelId);
    const latestVersion = versions.find(v => v.isActive);
    
    if (!latestVersion) {
      throw new Error('No active version found for redeployment');
    }

    const context: ChannelContext = { 
      channelId: 'recovery', 
      channelName: 'recovery', 
      guildId: 'system', 
      guildName: 'system',
      userId: 'system' 
    };
    const deploymentResult = await this.deployPanel(latestVersion.config, [context]);

    return {
      panelId,
      strategy: 'redeploy',
      success: deploymentResult.success,
      message: deploymentResult.success ? 'Panel successfully redeployed' : 'Redeployment failed',
      recoveredAt: new Date()
    };
  }

  private async identifyCleanupCandidates(): Promise<CleanupTask[]> {
    const tasks: CleanupTask[] = [];
    
    // Find inactive panels older than 30 days
    const result = await this.databaseService.query(
      `SELECT panel_id FROM panel_states 
       WHERE status = 'inactive' 
       AND updated_at < NOW() - INTERVAL '30 days'`
    );

    for (const row of result) {
      tasks.push({
        id: `cleanup_${row.panel_id}_${Date.now()}`,
        panelId: row.panel_id,
        type: 'soft',
        status: 'pending',
        createdAt: new Date()
      });
    }

    return tasks;
  }

  private async executeCleanupTask(task: CleanupTask): Promise<CleanupResult> {
    this.logger.debug(`Executing cleanup task ${task.id} for panel ${task.panelId}`);
    
    const cleanedItems: string[] = [];
    
    try {
      if (task.type === 'soft') {
        // Soft cleanup: remove cached data, old analytics
        await this.databaseService.query(
          'DELETE FROM panel_analytics WHERE panel_id = $1 AND created_at < NOW() - INTERVAL \'90 days\'',
          [task.panelId]
        );
        cleanedItems.push('old_analytics');
        
        // Clear memory caches
        // Implementation would clear relevant caches
        cleanedItems.push('memory_cache');
        
      } else if (task.type === 'hard') {
        // Hard cleanup: remove panel completely
        await this.panelCoreService.deactivatePanel(task.panelId);
        
        await this.databaseService.query('DELETE FROM panel_states WHERE panel_id = $1', [task.panelId]);
        await this.databaseService.query('DELETE FROM panel_analytics WHERE panel_id = $1', [task.panelId]);
        await this.databaseService.query('DELETE FROM panel_versions WHERE panel_id = $1', [task.panelId]);
        
        cleanedItems.push('panel_state', 'analytics', 'versions');
      }

      task.status = 'completed';
      
      return {
        panelId: task.panelId,
        success: true,
        cleanedItems
      };
      
    } catch (error) {
      task.status = 'failed';
      
      return {
        panelId: task.panelId,
        success: false,
        error: (error as Error).message,
        cleanedItems
      };
    }
  }

  // Additional utility methods would be implemented here...
  private generateDeploymentId(panelId: string): string {
    return `deploy_${panelId}_${Date.now()}`;
  }

  private calculateNextVersion(versions: PanelVersion[]): string {
    if (versions.length === 0) return '1.0.0';
    
    const latestVersion = versions.reduce((latest, current) => 
      current.version > latest.version ? current : latest
    );
    
    const parts = latestVersion.version.split('.').map(Number);
    parts[2]++; // Increment patch version
    
    return parts.join('.');
  }

  private async storeDeploymentRecord(task: DeploymentTask): Promise<void> {
    await this.databaseService.query(
      `INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)`,
      [
        task.id,
        task.panelId,
        JSON.stringify(task.config),
        JSON.stringify(task.targets),
        task.status,
        task.startedAt,
        task.completedAt,
        JSON.stringify(task.results),
        JSON.stringify(task.errors)
      ]
    );
  }

  private async storeVersionRecord(version: PanelVersion): Promise<void> {
    await this.databaseService.query(
      `INSERT INTO panel_versions (id, panel_id, version, config, change_notes, created_at, created_by, is_active, deployment_id)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)`,
      [
        version.id,
        version.panelId,
        version.version,
        JSON.stringify(version.config),
        version.changeNotes,
        version.createdAt,
        version.createdBy,
        version.isActive,
        version.deploymentId
      ]
    );
  }

  private generateDeploymentSummary(task: DeploymentTask): string {
    const successful = task.results.filter((r: any) => r.success).length;
    const total = task.targets.length;
    return `Deployed to ${successful}/${total} channels in ${task.completedAt ? 
      task.completedAt.getTime() - task.startedAt.getTime() : 0}ms`;
  }

  private async getDeploymentRecord(deploymentId: string): Promise<DeploymentTask | null> {
    const result = await this.databaseService.query(
      'SELECT * FROM panel_deployments WHERE id = $1',
      [deploymentId]
    );
    
    if (result.length === 0) return null;
    
    const row = result[0];
    return {
      id: row.id,
      panelId: row.panel_id,
      config: JSON.parse(row.config),
      targets: JSON.parse(row.targets),
      status: row.status,
      startedAt: row.started_at,
      completedAt: row.completed_at,
      results: JSON.parse(row.results || '[]'),
      errors: JSON.parse(row.errors || '[]')
    };
  }

  private async getPreviousVersion(panelId: string, channelId: string): Promise<PanelVersion | null> {
    const versions = await this.getVersionHistory(panelId);
    return versions.find((v, index) => index === 1) || null; // Second most recent
  }

  private async recordRollback(deploymentId: string, success: boolean, results: any[], errors: string[]): Promise<void> {
    await this.databaseService.query(
      `INSERT INTO panel_rollbacks (deployment_id, success, results, errors, created_at)
       VALUES ($1, $2, $3, $4, $5)`,
      [deploymentId, success, JSON.stringify(results), JSON.stringify(errors), new Date()]
    );
  }

  private async recordRecoveryAttempt(panelId: string, strategy: RecoveryStrategy, result: RecoveryResult): Promise<void> {
    await this.databaseService.query(
      `INSERT INTO panel_recovery_attempts (panel_id, strategy, success, message, recovered_at)
       VALUES ($1, $2, $3, $4, $5)`,
      [panelId, strategy, result.success, result.message, result.recoveredAt]
    );
  }

  private async getRecentErrors(panelId: string): Promise<any[]> {
    const result = await this.databaseService.query(
      `SELECT * FROM panel_errors 
       WHERE panel_id = $1 AND created_at > NOW() - INTERVAL '1 hour'
       ORDER BY created_at DESC`,
      [panelId]
    );
    
    return result;
  }

  private async loadActiveDeployments(): Promise<void> {
    try {
      const result = await this.databaseService.query(
        "SELECT * FROM panel_deployments WHERE status IN ('pending', 'running')"
      );
      
      for (const row of result) {
        const task: DeploymentTask = {
          id: row.id,
          panelId: row.panel_id,
          config: JSON.parse(row.config || '{}'),
          targets: JSON.parse(row.targets || '[]'),
          status: row.status,
          startedAt: row.started_at,
          completedAt: row.completed_at,
          results: JSON.parse(row.results || '[]'),
          errors: JSON.parse(row.errors || '[]')
        };
        
        this.deploymentQueue.set(task.id, task);
      }
      
      this.logger.log(`Loaded ${result.length} active deployments`);
    } catch (error) {
      if (error.code === '42P01') { // Table does not exist
        this.logger.warn('panel_deployments table does not exist yet. Run database migrations first.');
        this.logger.log('Skipping active deployment loading - service will work with empty state');
      } else {
        this.logger.error('Failed to load active deployments:', (error as Error).message);
      }
    }
  }

  private async loadVersionHistories(): Promise<void> {
    try {
      const result = await this.databaseService.query(
        'SELECT panel_id FROM panel_versions GROUP BY panel_id'
      );
      
      for (const row of result) {
        await this.getVersionHistory(row.panel_id);
      }
      
      this.logger.log(`Loaded version histories for ${result.length} panels`);
    } catch (error) {
      if (error.code === '42P01') { // Table does not exist
        this.logger.warn('panel_versions table does not exist yet. Run database migrations first.');
        this.logger.log('Skipping version history loading - service will work with empty state');
      } else {
        this.logger.error('Failed to load version histories:', (error as Error).message);
      }
    }
  }
}

// === TYPE DEFINITIONS ===

interface DeploymentTask {
  id: string;
  panelId: string;
  config: PanelConfig;
  targets: ChannelContext[];
  status: 'pending' | 'running' | 'completed' | 'failed';
  startedAt: Date;
  completedAt?: Date;
  results: ChannelDeploymentResult[];
  errors: string[];
}

interface DeploymentResult {
  deploymentId: string;
  success: boolean;
  channelResults: ChannelDeploymentResult[];
  errors: string[];
  summary: string;
}

interface ChannelDeploymentResult {
  channelId: string;
  success: boolean;
  deployedAt?: Date;
  error?: string;
}

interface RollbackResult {
  deploymentId: string;
  success: boolean;
  channelResults: ChannelRollbackResult[];
  errors: string[];
}

interface ChannelRollbackResult {
  channelId: string;
  success: boolean;
  restoredVersion?: string | null;
  error?: string;
}

interface PanelVersion {
  id: string;
  panelId: string;
  version: string;
  config: PanelConfig;
  changeNotes: string;
  createdAt: Date;
  createdBy: string;
  isActive: boolean;
  deploymentId: string | null;
}

type RecoveryStrategy = 'restart' | 'rollback' | 'redeploy';

interface RecoveryResult {
  panelId: string;
  strategy: RecoveryStrategy;
  success: boolean;
  message: string;
  recoveredAt: Date;
}

interface HealthCheckResult {
  panelId: string;
  isHealthy: boolean;
  issues: string[];
  recommendations: string[];
  lastChecked: Date;
}

type CleanupType = 'soft' | 'hard';

interface CleanupTask {
  id: string;
  panelId: string;
  type: CleanupType;
  status: 'pending' | 'running' | 'completed' | 'failed';
  createdAt: Date;
}

interface CleanupResult {
  panelId: string;
  success: boolean;
  cleanedItems: string[];
  error?: string;
}
