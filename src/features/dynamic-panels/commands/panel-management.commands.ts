import { Injectable, Logger } from '@nestjs/common';
import { EmbedBuilder, PermissionFlagsBits } from 'discord.js';
import { Context, Options, SlashCommand, SlashCommandContext, StringOption } from 'necord';
import { PanelConfigService } from '../services/panel-config.service';
import { PanelOrchestratorService } from '../services/panel-orchestrator.service';

export class PanelCommandDto {
  @StringOption({
    name: 'action',
    description: 'Panel management action',
    required: true,
    choices: [
      { name: 'Deploy Panel', value: 'deploy' },
      { name: 'Remove Panels', value: 'remove' },
      { name: 'List Panels', value: 'list' },
      { name: 'Refresh Channel', value: 'refresh' },
      { name: 'Panel Info', value: 'info' }
    ]
  })
  action!: string;

  @StringOption({
    name: 'panel_id',
    description: 'Panel ID (for deploy/info actions)',
    required: false
  })
  panelId?: string;

  @StringOption({
    name: 'channel',
    description: 'Target channel (for deploy/refresh actions)',
    required: false
  })
  channel?: string;

  @StringOption({
    name: 'filter',
    description: 'Filter panels by type/category',
    required: false,
    choices: [
      { name: 'AI Mastery', value: 'ai_mastery' },
      { name: 'Wealth Building', value: 'wealth_building' },
      { name: 'Personal Growth', value: 'personal_growth' },
      { name: 'Networking & Business', value: 'networking_business' },
      { name: 'AI Assistants', value: 'ai_assistants' },
      { name: 'Dev on Demand', value: 'dev_on_demand' },
      { name: 'Enterprise Support', value: 'enterprise_support' }
    ]
  })
  filter?: string;
}

@Injectable()
export class PanelManagementCommands {
  private readonly logger = new Logger(PanelManagementCommands.name);

  constructor(
    private readonly panelConfig: PanelConfigService,
    private readonly panelOrchestrator: PanelOrchestratorService
  ) {}

  @SlashCommand({
    name: 'panel',
    description: 'Manage dynamic panels in channels',
    defaultMemberPermissions: [PermissionFlagsBits.ManageChannels]
  })
  async onPanelCommand(
    @Context() [interaction]: SlashCommandContext,
    @Options() { action, panelId, channel, filter }: PanelCommandDto
  ) {
    try {
      switch (action) {
        case 'deploy':
          await this.deployPanel(interaction, panelId, channel);
          break;
        
        case 'remove':
          await this.removePanels(interaction, channel);
          break;
        
        case 'list':
          await this.listPanels(interaction, filter);
          break;
        
        case 'refresh':
          await this.refreshChannel(interaction, channel);
          break;
        
        case 'info':
          await this.showPanelInfo(interaction, panelId);
          break;
        
        default:
          await interaction.reply({
            content: '❌ Unknown action specified.',
            ephemeral: true
          });
      }
    } catch (error) {
      this.logger.error(`Failed to handle panel command: ${error instanceof Error ? error.message : String(error)}`, error);
      
      if (!interaction.replied && !interaction.deferred) {
        await interaction.reply({
          content: '❌ An error occurred while processing the panel command.',
          ephemeral: true
        });
      }
    }
  }

  private async deployPanel(
    interaction: any,
    panelId?: string,
    channel?: string
  ): Promise<void> {
    if (!panelId) {
      await interaction.reply({
        content: '❌ Panel ID is required for deployment.',
        ephemeral: true
      });
      return;
    }

    const config = this.panelConfig.getPanelConfig(panelId);
    if (!config) {
      await interaction.reply({
        content: `❌ Panel configuration not found: ${panelId}`,
        ephemeral: true
      });
      return;
    }

    // Use current channel if none specified
    const targetChannelId = channel ? 
      channel.replace(/[<#>]/g, '') : 
      interaction.channel.id;

    try {
      const deploymentId = await this.panelOrchestrator.deployPanel(
        panelId,
        targetChannelId,
        interaction.user.id,
        { manualDeploy: true }
      );

      await interaction.reply({
        content: `✅ **Panel deployed successfully!**\n\n📋 **Panel:** ${config.name}\n📍 **Channel:** <#${targetChannelId}>\n🆔 **Deployment ID:** ${deploymentId}`,
        ephemeral: true
      });
    } catch (error) {
      await interaction.reply({
        content: `❌ Failed to deploy panel: ${error instanceof Error ? error.message : String(error)}`,
        ephemeral: true
      });
    }
  }

  private async removePanels(
    interaction: any,
    channel?: string
  ): Promise<void> {
    // Use current channel if none specified
    const targetChannelId = channel ? 
      channel.replace(/[<#>]/g, '') : 
      interaction.channel.id;

    try {
      await this.panelOrchestrator.refreshChannelPanels(targetChannelId);

      await interaction.reply({
        content: `✅ **Panels removed successfully!**\n\n📍 **Channel:** <#${targetChannelId}>`,
        ephemeral: true
      });
    } catch (error) {
      await interaction.reply({
        content: `❌ Failed to remove panels: ${(error as Error).message}`,
        ephemeral: true
      });
    }
  }

  private async listPanels(
    interaction: any,
    filter?: string
  ): Promise<void> {
    try {
      let panels = this.panelConfig.getAllPanels();

      if (filter) {
        panels = panels.filter((panel: any) => panel.type === filter);
      }

      if (panels.length === 0) {
        await interaction.reply({
          content: '📭 No panels found matching your criteria.',
          ephemeral: true
        });
        return;
      }

      const embed = new EmbedBuilder()
        .setTitle('📋 Available Dynamic Panels')
        .setDescription(`Found ${panels.length} panel(s)${filter ? ` of type ${filter}` : ''}`)
        .setColor(0x3B82F6);

      panels.slice(0, 10).forEach(panel => {
        const tierEmoji = {
          'basic': '🔵',
          'premium': '🟡',
          'enterprise': '🔴'
        }[panel.requiredTier];

        const typeEmoji = {
          'ai_mastery': '🤖',
          'wealth_building': '💰',
          'dev_on_demand': '👨‍💻',
          'personal_growth': '🌱',
          'enterprise_support': '🏢'
        }[panel.type as keyof typeof typeEmoji] || '📋';

        embed.addFields([{
          name: `${typeEmoji} ${panel.name}`,
          value: [
            `**ID:** \`${panel.id}\``,
            `**Type:** ${panel.type}`,
            `**Tier:** ${tierEmoji} ${panel.requiredTier}`,
            `**Channels:** ${panel.channels.join(', ') || 'None'}`,
            `**Persistent:** ${panel.persistent ? '✅' : '❌'}`,
            `**Components:** ${panel.components.length}`
          ].join('\n'),
          inline: false
        }]);
      });

      if (panels.length > 10) {
        embed.setFooter({ text: `Showing first 10 of ${panels.length} panels` });
      }

      await interaction.reply({ embeds: [embed], ephemeral: true });
    } catch (error) {
      await interaction.reply({
        content: `❌ Failed to list panels: ${(error as Error).message}`,
        ephemeral: true
      });
    }
  }

  private async refreshChannel(
    interaction: any,
    channel?: string
  ): Promise<void> {
    // Use current channel if none specified
    const targetChannelId = channel ? 
      channel.replace(/[<#>]/g, '') : 
      interaction.channel.id;

    try {
      await this.panelOrchestrator.refreshChannelPanels(targetChannelId);

      await interaction.reply({
        content: `✅ **Channel panels refreshed!**\n\n📍 **Channel:** <#${targetChannelId}>\n\nNew panels will be deployed based on current activity and user tiers.`,
        ephemeral: true
      });
    } catch (error) {
      await interaction.reply({
        content: `❌ Failed to refresh channel: ${(error as Error).message}`,
        ephemeral: true
      });
    }
  }

  private async showPanelInfo(
    interaction: any,
    panelId?: string
  ): Promise<void> {
    if (!panelId) {
      await interaction.reply({
        content: '❌ Panel ID is required to show information.',
        ephemeral: true
      });
      return;
    }

    const config = this.panelConfig.getPanelConfig(panelId);
    if (!config) {
      await interaction.reply({
        content: `❌ Panel configuration not found: ${panelId}`,
        ephemeral: true
      });
      return;
    }

    const tierEmoji = {
      'basic': '🔵',
      'premium': '🟡',
      'enterprise': '🔴'
    }[config.requiredTier];

    const typeEmoji = {
      'ai_mastery': '🤖',
      'wealth_building': '💰',
      'dev_on_demand': '👨‍💻',
      'personal_growth': '🌱',
      'enterprise_support': '🏢'
    }[config.type as keyof typeof typeEmoji] || '📋';

    const embed = new EmbedBuilder()
      .setTitle(`${typeEmoji} ${config.name}`)
      .setDescription(config.description)
      .setColor(0x3B82F6)
      .addFields([
        { name: '🆔 Panel ID', value: `\`${config.id}\``, inline: true },
        { name: '📂 Type', value: config.type, inline: true },
        { name: '🎫 Required Tier', value: `${tierEmoji} ${config.requiredTier}`, inline: true },
        { name: '📍 Target Channels', value: config.channels.join(', ') || 'None', inline: false },
        { name: '📁 Categories', value: config.categories.join(', ') || 'None', inline: false },
        { name: '⚡ Triggers', value: config.triggers.join(', '), inline: false },
        { name: '🔧 Components', value: `${config.components.length} components`, inline: true },
        { name: '📌 Persistent', value: config.persistent ? '✅ Yes' : '❌ No', inline: true },
        { name: '⏱️ Auto-Delete', value: config.autoDelete ? `${config.autoDelete}m` : '❌ Never', inline: true }
      ]);

    if (config.requiredFeatures && config.requiredFeatures.length > 0) {
      embed.addFields([{
        name: '🎯 Required Features',
        value: config.requiredFeatures.join(', '),
        inline: false
      }]);
    }

    if (config.components.length > 0) {
      const componentsList = config.components
        .slice(0, 5)
        .map((comp: any) => `• ${comp.emoji || '🔘'} **${comp.label}** - ${comp.description || 'No description'}`)
        .join('\n');

      embed.addFields([{
        name: '🔧 Panel Components',
        value: componentsList + (config.components.length > 5 ? `\n... and ${config.components.length - 5} more` : ''),
        inline: false
      }]);
    }

    await interaction.reply({ embeds: [embed], ephemeral: true });
  }
}
