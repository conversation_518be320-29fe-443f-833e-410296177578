import { Injectable, Logger } from '@nestjs/common';
import { On, Context, ButtonContext } from 'necord';
import { Message, ButtonInteraction, GuildMember } from 'discord.js';
import { PanelOrchestratorService } from '../services/panel-orchestrator.service';
import { PanelInteractionHandler } from '../handlers/panel-interaction.handler';
import { ChannelFilterService } from '../../../core/services/channel-filter.service';

@Injectable()
export class DynamicPanelsListener {
  private readonly logger = new Logger(DynamicPanelsListener.name);

  constructor(
    private readonly panelOrchestrator: PanelOrchestratorService,
    private readonly panelInteractionHandler: PanelInteractionHandler,
    private readonly channelFilterService: ChannelFilterService
  ) {}

  @On('messageCreate')
  async onMessageCreate(@Context() [message]: [Message]): Promise<void> {
    try {
      // Use centralized channel filtering service
      if (!this.channelFilterService.shouldProcessMessage(message)) {
        return;
      }

      // Skip DMs
      if (!message.guild) return;

      // Process channel activity for panel automation
      await this.panelOrchestrator.processChannelActivity(
        message.channel.id,
        message.author.id,
        'message_sent',
        {
          messageContent: message.content,
          timestamp: message.createdAt
        }
      );
    } catch (error) {
      this.logger.error(`Failed to process message for panels: ${(error as Error).message}`, error);
    }
  }

  @On('guildMemberAdd')
  async onGuildMemberAdd(@Context() [member]: [GuildMember]): Promise<void> {
    try {
      // Process member join for welcome panels
      const channels = member.guild.channels.cache
        .filter((channel: any) => channel.isTextBased() && !channel.isVoiceBased())
        .filter((channel: any) => 'name' in channel && (
          channel.name.includes('welcome') || 
          channel.name.includes('general') ||
          channel.name.includes('start')
        ));

      for (const channel of channels.values()) {
        await this.panelOrchestrator.processChannelActivity(
          channel.id,
          member.id,
          'channel_join',
          {
            memberJoinedAt: member.joinedAt,
            isNewMember: true
          }
        );
      }
    } catch (error) {
      this.logger.error(`Failed to process member join for panels: ${(error as Error).message}`, error);
    }
  }

  @On('interactionCreate')
  async onInteractionCreate(@Context() [interaction]: [ButtonInteraction]): Promise<void> {
    try {
      if (!interaction.isButton()) return;
      if (!interaction.customId.startsWith('panel:')) return;

      await this.panelInteractionHandler.handlePanelButtonInteraction(
        [interaction] as ButtonContext
      );
    } catch (error) {
      this.logger.error(`Failed to handle panel interaction: ${(error as Error).message}`, error);
    }
  }

  @On('channelCreate')
  async onChannelCreate(@Context() [channel]: [any]): Promise<void> {
    try {
      if (!channel.guild) return;
      if (!channel.isTextBased()) return;

      // Setup category panels for new channels
      if (channel.parent) {
        await this.panelOrchestrator.setupCategoryPanels(channel.parent.id);
      }
    } catch (error) {
      this.logger.error(`Failed to setup panels for new channel: ${(error as Error).message}`, error);
    }
  }

  @On('channelUpdate')
  async onChannelUpdate(@Context() [oldChannel, newChannel]: [any, any]): Promise<void> {
    try {
      if (!newChannel.guild) return;
      if (!newChannel.isTextBased()) return;

      // Refresh panels if channel moved categories or name changed
      if (oldChannel.parent?.id !== newChannel.parent?.id || oldChannel.name !== newChannel.name) {
        await this.panelOrchestrator.refreshChannelPanels(newChannel.id);

        // Process new category setup
        if (newChannel.parent) {
          await this.panelOrchestrator.updateCategoryPanels(newChannel.parent.id, {
            channelMoved: true,
            oldParent: oldChannel.parent?.id,
            newParent: newChannel.parent.id
          });
        }
      }
    } catch (error) {
      this.logger.error(`Failed to update panels for channel change: ${(error as Error).message}`, error);
    }
  }

  // Utility method to trigger panel refresh for specific channels
  async refreshChannelPanels(channelIds: string[]): Promise<void> {
    try {
      for (const channelId of channelIds) {
        await this.panelOrchestrator.refreshChannelPanels(channelId);
      }
      this.logger.log(`Refreshed panels for ${channelIds.length} channels`);
    } catch (error) {
      this.logger.error(`Failed to refresh channel panels: ${(error as Error).message}`, error);
    }
  }

  // Utility method to sync panels for user tier changes
  async syncUserPanels(userId: string, newTier: 'basic' | 'premium' | 'enterprise'): Promise<void> {
    try {
      await this.panelOrchestrator.syncMemberPanels(userId, newTier);
      this.logger.log(`Synced panels for user ${userId} with tier ${newTier}`);
    } catch (error) {
      this.logger.error(`Failed to sync user panels: ${(error as Error).message}`, error);
    }
  }
}
