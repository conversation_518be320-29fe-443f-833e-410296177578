import { Injectable, Logger } from '@nestjs/common';
import { Em<PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, ButtonBuilder, ButtonStyle, TextChannel } from 'discord.js';
import { RedisDatabaseService } from '@/core/database';
import { PanelConfigService } from './panel-config.service';
import { MembershipService } from '../../../core/services/membership.service';
import { Client } from 'discord.js';
import { 
  IPanelOrchestrator, 
  PanelConfiguration, 
  PanelTrigger, 
  MembershipTier,
  PanelDeployment 
} from '../interfaces/panel.interface';

// Server Category Configuration
const CATEGORY_CONFIG = {
  // Allowed Categories (panels will work here)
  ALLOWED_CATEGORIES: {
    'ANNOUNCEMENTS': '1396352166536548407',
    'COMMUNITY': '1396352189206757376', 
    'AI_MASTERY': '1396594612654702744',
    'WEALTH_CREATION': '1396594632699416757',
    'PERSONAL_GROWTH': '1396594648335777803',
    'NETWORKING_BUSINESS': '1396594691818127380',
    'AI_ASSISTANTS': '1398998360605855827',
    'DEV_ON_DEMAND': '1396594670179586218',
    'VOICE_CHANNELS': '1394355427612823725',
    'GAMING': '1396352214796079115'
  },
  
  // Excluded Categories (no AI automation)
  EXCLUDED_CATEGORIES: {
    'STAFF': '1394712469867466905',
    'DEVELOPMENT': '1394721385774973071', 
    'SUPPORT': '1396352237315424278'
  },

  // Category Name Mappings
  CATEGORY_MAPPINGS: {
    '1396352166536548407': '📢 ANNOUNCEMENTS',
    '1396352189206757376': '💬 COMMUNITY',
    '1396594612654702744': '🧠 AI MASTERY', 
    '1396594632699416757': '💰 WEALTH CREATION',
    '1396594648335777803': '🌱 PERSONAL GROWTH',
    '1396594691818127380': '🤝 NETWORKING & BUSINESS',
    '1398998360605855827': 'AI Assistants ⭐',
    '1396594670179586218': '⚡ DEV ON DEMAND',
    '1394355427612823725': 'Voice Channels',
    '1396352214796079115': '🎮 GAMING'
  }
};

@Injectable()
export class PanelOrchestratorService implements IPanelOrchestrator {
  private readonly logger = new Logger(PanelOrchestratorService.name);
  private readonly activePanels = new Map<string, PanelDeployment>();
  private readonly userCooldowns = new Map<string, Map<string, number>>();

  constructor(
    private readonly redisDatabaseService: RedisDatabaseService,
    private readonly panelConfig: PanelConfigService,
    private readonly membershipService: MembershipService,
    private readonly client: Client
  ) {
    // Clean up expired panels every 5 minutes
    setInterval(() => this.cleanupExpiredPanels(), 5 * 60 * 1000);
  }

  async processChannelActivity(
    channelId: string, 
    userId: string, 
    trigger: PanelTrigger, 
    context?: any
  ): Promise<void> {
    try {
      this.logger.debug(`Processing channel activity: ${channelId}, user: ${userId}, trigger: ${trigger}`);

      // Get channel info to determine category
      const channel = this.client.channels.cache.get(channelId);
      if (!channel || !('guild' in channel)) return;

      // Check if channel category is excluded from automation
      if (channel.parent && this.isCategoryExcluded(channel.parent.id)) {
        this.logger.debug(`Skipping panel automation for excluded category: ${channel.parent.name}`);
        return;
      }

      const channelName = channel.name;
      const categoryId = channel.parent?.id;
      const categoryName = this.getCategoryDisplayName(categoryId);

      // Get user's membership tier
      const userContext = await this.membershipService.getUserContext(userId, channel.guild.id);
      if (!userContext) return;

      // Find applicable panels
      const applicablePanels = await this.detectPanelsForChannel(channelId, userId, trigger);
      
      for (const panelConfig of applicablePanels) {
        // Check if panel should be deployed
        if (await this.shouldDeployPanel(panelConfig, channelId, userId)) {
          await this.deployPanel(panelConfig.id, channelId, userId, {
            trigger,
            channelName,
            categoryName,
            userTier: userContext.tier,
            ...context
          });
        }
      }
    } catch (error) {
      this.logger.error(`Failed to process channel activity: ${(error as Error).message}`, error);
    }
  }

  async detectPanelsForChannel(
    channelId: string, 
    userId: string, 
    trigger: PanelTrigger
  ): Promise<PanelConfiguration[]> {
    try {
      const channel = this.client.channels.cache.get(channelId);
      if (!channel || !('guild' in channel)) return [];

      // Check if channel category is excluded from automation
      if (channel.parent && this.isCategoryExcluded(channel.parent.id)) {
        return [];
      }

      const channelName = channel.name;
      const categoryId = channel.parent?.id;
      const categoryName = this.getCategoryDisplayName(categoryId);

      // Get user's tier and features
      const userContext = await this.membershipService.getUserContext(userId, channel.guild.id);
      if (!userContext) return [];

      // Find panels that match channel, category, and trigger
      const allPanels = this.panelConfig.getAllPanels();
      const applicablePanels: PanelConfiguration[] = [];

      for (const panel of allPanels) {
        // Check trigger match
        if (!panel.triggers.includes(trigger)) continue;

        // Check channel/category match
        const channelMatch = panel.channels.some(ch => 
          channelName.includes(ch) || ch.includes(channelName)
        );
        const categoryMatch = panel.categories.some(cat => 
          categoryName.toUpperCase().includes(cat) || cat.includes(categoryName.toUpperCase())
        );

        if (!channelMatch && !categoryMatch) continue;

        // Check tier access
        if (!this.checkTierAccess(userContext.tier, panel.requiredTier)) continue;

        // Check feature access
        if (panel.requiredFeatures && panel.requiredFeatures.length > 0) {
          const hasAllFeatures = panel.requiredFeatures.every(feature => 
            userContext.features.includes(feature)
          );
          if (!hasAllFeatures) continue;
        }

        applicablePanels.push(panel);
      }

      this.logger.debug(`Found ${applicablePanels.length} applicable panels for channel ${channelName}`);
      return applicablePanels;
    } catch (error) {
      this.logger.error(`Failed to detect panels for channel: ${(error as Error).message}`, error);
      return [];
    }
  }

  async shouldDeployPanel(
    config: PanelConfiguration, 
    channelId: string, 
    userId: string
  ): Promise<boolean> {
    try {
      // Check cooldown
      if (this.isUserOnCooldown(userId, config.id, config.cooldown || 60)) {
        return false;
      }

      // Check if persistent panel already exists
      if (config.persistent) {
        const existingPanel = Array.from(this.activePanels.values()).find(
          panel => panel.panelId === config.id && 
                   panel.channelId === channelId && 
                   panel.userId === userId
        );
        if (existingPanel) return false;
      }

      // Check usage limits
      if (config.maxUsesPerUser) {
        const userUsage = this.getUserPanelUsage(userId, config.id);
        if (userUsage >= config.maxUsesPerUser) return false;
      }

      return true;
    } catch (error) {
      this.logger.error(`Failed to check panel deployment eligibility: ${(error as Error).message}`, error);
      return false;
    }
  }

  async deployPanel(
    panelId: string, 
    channelId: string, 
    userId: string, 
    context?: any
  ): Promise<string> {
    try {
      const config = this.panelConfig.getPanelConfig(panelId);
      if (!config) throw new Error(`Panel configuration not found: ${panelId}`);

      const channel = this.client.channels.cache.get(channelId) as TextChannel;
      if (!channel) throw new Error(`Channel not found: ${channelId}`);

      // Create embed
      const embed = this.createPanelEmbed(config, context);

      // Create action row with buttons
      const actionRow = this.createPanelActionRow(config, userId);

      // Send panel message
      const message = await channel.send({
        embeds: [embed],
        components: actionRow ? [actionRow] : []
      });

      // Create deployment record
      const deploymentId = `${panelId}_${channelId}_${userId}_${Date.now()}`;
      const deployment: PanelDeployment = {
        id: deploymentId,
        panelId,
        channelId,
        messageId: message.id,
        userId,
        deployedAt: new Date(),
        expiresAt: config.autoDelete ? new Date(Date.now() + config.autoDelete * 60 * 1000) : new Date(Date.now() + 24 * 60 * 60 * 1000),
        usageCount: 0
      };

      this.activePanels.set(deploymentId, deployment);

      // Set user cooldown
      this.setUserCooldown(userId, panelId, config.cooldown || 60);

      this.logger.log(`Deployed panel ${panelId} to channel ${channelId} for user ${userId}`);
      
      // Schedule auto-deletion if needed
      if (config.autoDelete) {
        setTimeout(() => this.removePanel(deploymentId), config.autoDelete * 60 * 1000);
      }

      return deploymentId;
    } catch (error) {
      this.logger.error(`Failed to deploy panel: ${(error as Error).message}`, error);
      throw error;
    }
  }

  async removePanel(deploymentId: string): Promise<void> {
    try {
      const deployment = this.activePanels.get(deploymentId);
      if (!deployment) return;

      // Delete Discord message
      try {
        const channel = this.client.channels.cache.get(deployment.channelId) as TextChannel;
        if (channel) {
          const message = await channel.messages.fetch(deployment.messageId);
          if (message) await message.delete();
        }
      } catch (error) {
        this.logger.warn(`Failed to delete panel message: ${(error as Error).message}`);
      }

      // Remove from active panels
      this.activePanels.delete(deploymentId);
      this.logger.debug(`Removed panel deployment: ${deploymentId}`);
    } catch (error) {
      this.logger.error(`Failed to remove panel: ${(error as Error).message}`, error);
    }
  }

  async refreshChannelPanels(channelId: string): Promise<void> {
    try {
      // Remove all panels for this channel
      const channelPanels = Array.from(this.activePanels.entries())
        .filter(([_, deployment]) => deployment.channelId === channelId);

      for (const [deploymentId] of channelPanels) {
        await this.removePanel(deploymentId);
      }

      this.logger.log(`Refreshed panels for channel: ${channelId}`);
    } catch (error) {
      this.logger.error(`Failed to refresh channel panels: ${(error as Error).message}`, error);
    }
  }

  async cleanupExpiredPanels(): Promise<void> {
    try {
      const now = new Date();
      const expiredPanels = Array.from(this.activePanels.entries())
        .filter(([_, deployment]) => deployment.expiresAt && deployment.expiresAt <= now);

      for (const [deploymentId] of expiredPanels) {
        await this.removePanel(deploymentId);
      }

      if (expiredPanels.length > 0) {
        this.logger.log(`Cleaned up ${expiredPanels.length} expired panels`);
      }
    } catch (error) {
      this.logger.error(`Failed to cleanup expired panels: ${(error as Error).message}`, error);
    }
  }

  async setupCategoryPanels(categoryId: string): Promise<void> {
    try {
      this.logger.log(`Setting up panels for category: ${categoryId}`);
      // Implementation depends on specific category needs
      // This would be called when a new category is created or updated
    } catch (error) {
      this.logger.error(`Failed to setup category panels: ${(error as Error).message}`, error);
    }
  }

  async updateCategoryPanels(categoryId: string, changes: any): Promise<void> {
    try {
      this.logger.log(`Updating panels for category: ${categoryId}`);
      // Implementation for when category settings change
    } catch (error) {
      this.logger.error(`Failed to update category panels: ${(error as Error).message}`, error);
    }
  }

  async syncMemberPanels(userId: string, newTier: MembershipTier): Promise<void> {
    try {
      this.logger.log(`Syncing panels for user ${userId} with new tier: ${newTier}`);
      
      // Remove panels user no longer has access to
      const userPanels = Array.from(this.activePanels.entries())
        .filter(([_, deployment]) => deployment.userId === userId);

      for (const [deploymentId, deployment] of userPanels) {
        const config = this.panelConfig.getPanelConfig(deployment.panelId);
        if (config && !this.checkTierAccess(newTier, config.requiredTier)) {
          await this.removePanel(deploymentId);
        }
      }
    } catch (error) {
      this.logger.error(`Failed to sync member panels: ${(error as Error).message}`, error);
    }
  }

  async validatePanelAccess(userId: string, panelId: string): Promise<boolean> {
    try {
      const config = this.panelConfig.getPanelConfig(panelId);
      if (!config) return false;

      // Get user context (this would need guild ID in real implementation)
      // For now, return true for basic validation
      return true;
    } catch (error) {
      this.logger.error(`Failed to validate panel access: ${(error as Error).message}`, error);
      return false;
    }
  }

  private createPanelEmbed(config: PanelConfiguration, context?: any): EmbedBuilder {
    const embed = new EmbedBuilder();

    if (config.embedData) {
      embed.setTitle(config.embedData.title);
      embed.setDescription(config.embedData.description);
      embed.setColor(config.embedData.color);

      if (config.embedData.thumbnail) {
        embed.setThumbnail(config.embedData.thumbnail);
      }

      if (config.embedData.fields) {
        config.embedData.fields.forEach(field => {
          embed.addFields({
            name: field.name,
            value: field.value,
            inline: field.inline || false
          });
        });
      }
    } else {
      embed.setTitle(config.name);
      embed.setDescription(config.description);
      embed.setColor(0x3B82F6);
    }

    embed.setTimestamp();
    embed.setFooter({ text: '⚡ EnergeX Dynamic Command System • Your Empire Awaits' });

    return embed;
  }

  private createPanelActionRow(config: PanelConfiguration, userId: string): ActionRowBuilder<ButtonBuilder> | null {
    const buttons = config.components
      .filter((component: any) => component.type === 'button')
      .slice(0, 5); // Discord limit

    if (buttons.length === 0) return null;

    const actionRow = new ActionRowBuilder<ButtonBuilder>();

    buttons.forEach(button => {
      const discordButton = new ButtonBuilder()
        .setCustomId(`panel:${config.id}:${button.action}:${userId}`)
        .setLabel(button.label)
        .setStyle(this.getButtonStyle(button.style || 'secondary'));

      if (button.emoji) {
        discordButton.setEmoji(button.emoji);
      }

      actionRow.addComponents(discordButton);
    });

    return actionRow;
  }

  private getButtonStyle(style: string): ButtonStyle {
    switch (style) {
      case 'primary': return ButtonStyle.Primary;
      case 'success': return ButtonStyle.Success;
      case 'danger': return ButtonStyle.Danger;
      case 'secondary':
      default: return ButtonStyle.Secondary;
    }
  }

  private checkTierAccess(userTier: MembershipTier, requiredTier: MembershipTier): boolean {
    const tierLevels = { basic: 1, premium: 2, enterprise: 3 };
    return tierLevels[userTier] >= tierLevels[requiredTier];
  }

  private isUserOnCooldown(userId: string, panelId: string, cooldownSeconds: number): boolean {
    const userCooldowns = this.userCooldowns.get(userId);
    if (!userCooldowns) return false;

    const lastUsed = userCooldowns.get(panelId);
    if (!lastUsed) return false;

    return (Date.now() - lastUsed) < (cooldownSeconds * 1000);
  }

  private setUserCooldown(userId: string, panelId: string, cooldownSeconds: number): void {
    if (!this.userCooldowns.has(userId)) {
      this.userCooldowns.set(userId, new Map());
    }
    this.userCooldowns.get(userId)!.set(panelId, Date.now());
  }

  private getUserPanelUsage(userId: string, panelId: string): number {
    return Array.from(this.activePanels.values())
      .filter((deployment: any) => deployment.userId === userId && deployment.panelId === panelId)
      .reduce((total, deployment) => total + deployment.usageCount, 0);
  }

  /**
   * Check if a category is excluded from panel automation
   */
  private isCategoryExcluded(categoryId: string): boolean {
    return Object.values(CATEGORY_CONFIG.EXCLUDED_CATEGORIES).includes(categoryId);
  }

  /**
   * Get display name for category ID
   */
  private getCategoryDisplayName(categoryId?: string): string {
    if (!categoryId) return 'GENERAL';
    return (CATEGORY_CONFIG.CATEGORY_MAPPINGS as Record<string, string>)[categoryId] || 'UNKNOWN';
  }

  /**
   * Check if category is allowed for panel automation  
   */
  private isCategoryAllowed(categoryId?: string): boolean {
    if (!categoryId) return false;
    return Object.values(CATEGORY_CONFIG.ALLOWED_CATEGORIES).includes(categoryId);
  }
}
