import { AIProvider } from '@/core/database';

export interface ProviderValidationPattern {
  pattern: RegExp;
  message: string;
}

export interface ProviderConfig {
  id: AIProvider;
  name: string;
  displayName: string;
  description: string;
  websiteUrl: string;
  apiKeyUrl: string;
  keyFormat: string;
  validation: ProviderValidationPattern;
  instructions: string[];
  models: string[];
  features: string[];
  requiresVerification?: boolean;
  testEndpoint?: string;
  icon: string;
  color: string;
}

export const PROVIDER_CONFIGS: Record<AIProvider, ProviderConfig> = {
  openai: {
    id: 'openai',
    name: 'OpenAI',
    displayName: 'OpenAI (GPT-4, ChatGPT)',
    description: 'Access to GPT-4, GPT-3.5-turbo, and other OpenAI models',
    websiteUrl: 'https://openai.com',
    apiKeyUrl: 'https://platform.openai.com/api-keys',
    keyFormat: 'sk-...7 or sk-proj-...T',
    validation: {
      pattern: /^sk-(proj-)?[a-zA-Z0-9]{20,}$/,
      message: 'OpenAI API keys start with "sk-" or "sk-proj-" followed by at least 20 characters'
    },
    instructions: [
      '1. Visit platform.openai.com and sign in',
      '2. Go to API Keys section',
      '3. Click "Create new secret key"',
      '4. Copy the key (starts with sk-)',
      '5. Paste it in the form below'
    ],
    models: [
      'gpt-4o',
      'gpt-4o-mini', 
      'gpt-4-turbo',
      'gpt-3.5-turbo',
      'text-embedding-3-large',
      'dall-e-3'
    ],
    features: [
      'Text Generation',
      'Code Generation', 
      'Creative Writing',
      'Text Embeddings',
      'Function Calling'
    ],
    requiresVerification: true,
    testEndpoint: 'https://api.openai.com/v1/models',
    icon: '🤖',
    color: '#10a37f'
  },

  anthropic: {
    id: 'anthropic',
    name: 'Anthropic',
    displayName: 'Anthropic (Claude)',
    description: 'Access to Claude 3 models from Anthropic',
    websiteUrl: 'https://anthropic.com',
    apiKeyUrl: 'https://console.anthropic.com/account/keys',
    keyFormat: 'sk-ant-api03-...AA',
    validation: {
      pattern: /^sk-ant-api03-[a-zA-Z0-9_-]{95}AA$/,
      message: 'Anthropic API keys start with "sk-ant-api03-" and end with "AA"'
    },
    instructions: [
      '1. Visit console.anthropic.com and sign in',
      '2. Go to Account → API Keys',
      '3. Click "Create Key"', 
      '4. Copy the key (starts with sk-ant-api03-)',
      '5. Paste it in the form below'
    ],
    models: [
      'claude-3-5-sonnet-********',
      'claude-3-5-haiku-********',
      'claude-3-opus-********',
      'claude-3-sonnet-********',
      'claude-3-haiku-********'
    ],
    features: [
      'Advanced Reasoning',
      'Long Context',
      'Code Analysis',
      'Document Analysis',
      'Creative Writing'
    ],
    requiresVerification: true,
    testEndpoint: 'https://api.anthropic.com/v1/messages',
    icon: '🧠',
    color: '#d4a574'
  },

  google: {
    id: 'google',
    name: 'Google',
    displayName: 'Google AI (Gemini)',
    description: 'Access to Gemini Pro and other Google AI models',
    websiteUrl: 'https://ai.google.dev',
    apiKeyUrl: 'https://makersuite.google.com/app/apikey',
    keyFormat: 'AIza...ABC',
    validation: {
      pattern: /^AIza[a-zA-Z0-9_-]{35}$/,
      message: 'Google AI API keys start with "AIza" followed by 35 characters'
    },
    instructions: [
      '1. Visit ai.google.dev and sign in',
      '2. Go to Google AI Studio',
      '3. Click "Get API key"',
      '4. Copy the key (starts with AIza)',
      '5. Paste it in the form below'
    ],
    models: [
      'gemini-1.5-pro-002',
      'gemini-1.5-flash-002',
      'gemini-1.5-flash-8b',
      'gemini-1.0-pro',
      'text-embedding-004'
    ],
    features: [
      'Multimodal AI',
      'Vision Understanding',
      'Long Context',
      'Code Generation',
      'Real-time Processing'
    ],
    requiresVerification: true,
    testEndpoint: 'https://generativelanguage.googleapis.com/v1/models',
    icon: '🌟',
    color: '#4285f4'
  },

  azure: {
    id: 'azure',
    name: 'Azure',
    displayName: 'Azure OpenAI Service',
    description: 'Enterprise OpenAI models through Microsoft Azure',
    websiteUrl: 'https://azure.microsoft.com/en-us/products/ai-services/openai-service',
    apiKeyUrl: 'https://portal.azure.com',
    keyFormat: 'endpoint + key',
    validation: {
      pattern: /^[a-f0-9]{32}$/,
      message: 'Azure OpenAI keys are 32-character hexadecimal strings'
    },
    instructions: [
      '1. Visit Azure Portal and sign in',
      '2. Go to your OpenAI resource',
      '3. Navigate to Keys and Endpoint',
      '4. Copy Key 1 or Key 2',
      '5. Also note your endpoint URL'
    ],
    models: [
      'azure-gpt-4o',
      'azure-gpt-4-turbo', 
      'azure-gpt-35-turbo'
    ],
    features: [
      'Enterprise Security',
      'Data Residency',
      'Private Networks',
      'Compliance',
      'Custom Models'
    ],
    requiresVerification: true,
    icon: '☁️',
    color: '#0078d4'
  },

  exa: {
    id: 'exa',
    name: 'Exa',
    displayName: 'Exa (AI Web Search)',
    description: 'Advanced neural web search and real-time information retrieval',
    websiteUrl: 'https://exa.ai',
    apiKeyUrl: 'https://dashboard.exa.ai/api-keys',
    keyFormat: 'exa_...ABC',
    validation: {
      pattern: /^exa_[a-zA-Z0-9]{32,}$/,
      message: 'Exa API keys start with "exa_" followed by at least 32 characters'
    },
    instructions: [
      '1. Visit dashboard.exa.ai and sign in',
      '2. Go to API Keys section',
      '3. Click "Create API Key"',
      '4. Copy the key (starts with exa_)',
      '5. Paste it in the form below'
    ],
    models: [
      'exa-web-search',
      'exa-neural-search',
      'exa-content-extraction',
      'exa-find-similar'
    ],
    features: [
      'Neural Web Search',
      'Content Extraction',
      'Similar Page Discovery',
      'Real-time Information',
      'Source Citations',
      'Advanced Filtering'
    ],
    requiresVerification: true,
    testEndpoint: 'https://api.exa.ai/search',
    icon: '🔍',
    color: '#7c3aed'
  },

  custom: {
    id: 'custom',
    name: 'Custom',
    displayName: 'Custom API Provider',
    description: 'Use your own API endpoint or third-party service',
    websiteUrl: '',
    apiKeyUrl: '',
    keyFormat: 'Any format',
    validation: {
      pattern: /.{1,}/,
      message: 'Custom API keys can be any format'
    },
    instructions: [
      '1. Get your API key from your provider',
      '2. Make sure it has the required permissions',
      '3. Test it works with your service',
      '4. Enter the key and endpoint details',
      '5. Configure any additional settings'
    ],
    models: [
      'custom-model-1',
      'custom-model-2'
    ],
    features: [
      'Flexible Integration',
      'Custom Endpoints',
      'Any Model Type',
      'Self-hosted Options'
    ],
    requiresVerification: false,
    icon: '🔧',
    color: '#6c757d'
  }
};

export function getProviderConfig(provider: AIProvider): ProviderConfig {
  return PROVIDER_CONFIGS[provider];
}

export function getAllProviders(): ProviderConfig[] {
  return Object.values(PROVIDER_CONFIGS);
}

export function validateApiKey(provider: AIProvider, apiKey: string): { isValid: boolean; message?: string } {
  const config = getProviderConfig(provider);
  
  if (!config) {
    return { isValid: false, message: 'Unknown provider' };
  }

  const isValid = config.validation.pattern.test(apiKey);
  
  return {
    isValid,
    ...(isValid ? {} : { message: config.validation.message })
  };
}
